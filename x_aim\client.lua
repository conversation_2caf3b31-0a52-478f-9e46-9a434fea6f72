local InAim = false
local Score = 0
local reset = false 
local beforecoords = nil
local exitTextShown = false

RegisterCommand('leaveaim', function()
    if not reset then 
        reset = true 
    end
end, false)

RegisterCommand(Config.CommandName, function()
    if not InAim then 
        beforecoords = GetEntityCoords(PlayerPedId())
        InAim = true
        NetworkStartSoloTutorialSession()
        TriggerServerEvent('aim:enterBucket') -- Enter player-specific bucket
        SetNuiFocus(true, true)
        SendNUIMessage({
            type = 'ui'
        })
    end
end, false)

RegisterNuiCallback('call', function(data, cb)
    if data.selectedValue == 'PISTOL50' then 
        GiveWeaponToPed(PlayerPedId(), GetHashKey('WEAPON_PISTOL50'), 480, false, true)
    elseif data.selectedValue == 'glock' then 
        GiveWeaponToPed(PlayerPedId(), GetHashKey('WEAPON_COMBATPISTOL'), 480, false, true)
    end
    
    if data.aim == 'aimtraining' then
        SetNuiFocus(false, false)
        SetPedInfiniteAmmo(PlayerPedId(), true, GetSelectedPedWeapon(PlayerPedId()))
        RefillAmmoInstantly(PlayerPedId())
        SetEntityAlpha(PlayerPedId(), 128)
        exitTextShown = true
        aimtraining()
    elseif data.aim == 'npcroll' then
        SetNuiFocus(false, false)
        SetPedInfiniteAmmo(PlayerPedId(), true, GetSelectedPedWeapon(PlayerPedId()))
        RefillAmmoInstantly(PlayerPedId())
        SetEntityAlpha(PlayerPedId(), 128)
        exitTextShown = true
        npcroll()
    end
end)

RegisterNuiCallback('exit', function(data, cb)
    InAim = false
    exitTextShown = false
    SetEntityCoords(PlayerPedId(), beforecoords.x, beforecoords.y, beforecoords.z - 1)
    FreezeEntityPosition(PlayerPedId(), true)
    SetNuiFocus(false, false)
    TriggerServerEvent('aim:exitBucket') -- Exit player-specific bucket
    Wait(2000)
    FreezeEntityPosition(PlayerPedId(), false)
    SetEntityAlpha(PlayerPedId(), 255)
    NetworkEndTutorialSession()
end)

Citizen.CreateThread(function()
    while true do 
        Wait(800)
        if HasPedGotWeapon(PlayerPedId(), GetHashKey('WEAPON_x'), false) and not InAim then 
            RemoveWeaponFromPed(PlayerPedId(), GetHashKey('WEAPON_x'))
        end
        if HasPedGotWeapon(PlayerPedId(), GetHashKey('WEAPON_x'), false) and not InAim then 
            RemoveWeaponFromPed(PlayerPedId(), GetHashKey('WEAPON_xL'))
        end
    end
end)

Citizen.CreateThread(function()
    sleep = 480
    while true do 
        Wait(sleep)
        if InAim then 
            sleep = 0
            if IsPedReloading(PlayerPedId()) then 
                RefillAmmoInstantly(PlayerPedId())
            end
            
            if exitTextShown then
                DrawExitText()
            end
        else
            sleep = 480
        end
    end
end)

function DrawExitText()
    SetTextFont(4)
    SetTextScale(0.5, 0.5)
    SetTextColour(255, 255, 255, 180)
    SetTextDropshadow(0, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextCentre(true)
    SetTextEntry("STRING")
    AddTextComponentString("Press ~r~E~w~ or type ~r~/leaveaim~w~ to exit")
    DrawText(0.5, 0.92)
end

function ShowSubtitle(message, duration)
    BeginTextCommandPrint('STRING')
    AddTextComponentString(message)
    EndTextCommandPrint(duration, true)
end

function aimtraining()
    SetEntityCoords(PlayerPedId(), Config.StandingPosition.x, Config.StandingPosition.y, Config.StandingPosition.z - 1)
    FreezeEntityPosition(PlayerPedId(), true)
    SetEntityHeading(PlayerPedId(), Config.StandingHeading)
    SetPedInfiniteAmmo(PlayerPedId(), true, GetSelectedPedWeapon(PlayerPedId()))
    
    -- Create a blip at the training location
    local blip = AddBlipForCoord(Config.StandingPosition.x, Config.StandingPosition.y, Config.StandingPosition.z)
    SetBlipSprite(blip, 313) -- Shooting range blip
    SetBlipColour(blip, 1) -- Red
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Aim Training")
    EndTextCommandSetBlipName(blip)
    
    Citizen.CreateThread(function()
        Score = 0
        Citizen.Wait(3000)
        while true do 
            Citizen.Wait(0)
            if not reset then 
                for k,v in pairs(Config.NpcKilling.Positions) do 
                    if reset then 
                        break
                    end
                    RequestModel(GetHashKey(Config.NpcKilling.Skin))
                    while not HasModelLoaded(GetHashKey(Config.NpcKilling.Skin)) do Citizen.Wait(8) end 
                    local NPC = CreatePed(1, GetHashKey(Config.NpcKilling.Skin), v.x, v.y, v.z -1, 160.0, false, false)
                    FreezeEntityPosition(NPC, true)
                    StopPedSpeaking(NPC, true)
                    SetRagdollBlockingFlags(NPC, 1)
                    SetBlockingOfNonTemporaryEvents(NPC, true)
                    Citizen.Wait(1000)
                    if IsPedDeadOrDying(NPC) then 
                        Score = Score + 20
                        ShowSubtitle('Score : ' .. Score, 2000)
                        SendNUIMessage({
                            type = 'ding'
                        })
                    end
                    DeletePed(NPC)
                end
            else
                FreezeEntityPosition(PlayerPedId(), false)
                InAim = true
                exitTextShown = false
                SetNuiFocus(true, true)
                ShowSubtitle('', 0)
                SendNUIMessage({
                    type = 'score',
                    score = Score
                })
                -- Log score to webhook
                TriggerServerEvent('aim:logScore', 'NPC Training', Score)
                RemoveWeaponFromPed(PlayerPedId(), GetHashKey('WEAPON_x'))
                RemoveWeaponFromPed(PlayerPedId(), GetHashKey('WEAPON_xL'))
                RemoveBlip(blip)
                Citizen.Wait(2800)
                SendNUIMessage({
                    type = 'ui'
                })
                SetEntityAlpha(PlayerPedId(), 255)
                reset = false
                break
            end
        end
    end)
end

function npcroll()
    Score = 0
    SetEntityCoords(PlayerPedId(), Config.StandingPosition.x, Config.StandingPosition.y, Config.StandingPosition.z - 1)
    FreezeEntityPosition(PlayerPedId(), true)
    SetEntityHeading(PlayerPedId(), Config.StandingHeading)
    SetPedInfiniteAmmo(PlayerPedId(), true, GetSelectedPedWeapon(PlayerPedId()))
    
    -- Create a blip at the training location
    local blip = AddBlipForCoord(Config.StandingPosition.x, Config.StandingPosition.y, Config.StandingPosition.z)
    SetBlipSprite(blip, 313) -- Shooting range blip
    SetBlipColour(blip, 1) -- Red
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Aim Training")
    EndTextCommandSetBlipName(blip)
    
    Citizen.Wait(3000)
    Citizen.CreateThread(function()
        while true do 
            Citizen.Wait(0)
            if not reset then 
                for k,v in pairs(Config.NpcKilling.Positions) do 
                    if reset then 
                        break 
                    end
                    RequestModel(GetHashKey(Config.NpcKilling.Skin))
                    while not HasModelLoaded(GetHashKey(Config.NpcKilling.Skin)) do Citizen.Wait(8) end 
                    local NPC = CreatePed(1, GetHashKey(Config.NpcKilling.Skin), v.x, v.y, v.z -1, 160.0, false, false)
                    FreezeEntityPosition(NPC, true)
                    StopPedSpeaking(NPC, true)
                    SetRagdollBlockingFlags(NPC, 1)
                    SetBlockingOfNonTemporaryEvents(NPC, true)
                    local animationDict = "move_strafe@roll_fps"
                    local animationName = "combatroll_bwd_p1_135"
                    RequestAnimDict(animationDict)
                    while not HasAnimDictLoaded(animationDict) do
                        Wait(0)
                    end
                    local playbackRate = 4.0 
                    TaskPlayAnim(NPC, animationDict, animationName, playbackRate, -playbackRate, 600, 1, 0, false, false, false)
                    Citizen.Wait(1480)
                    if IsPedDeadOrDying(NPC) then 
                        Score = Score + 20
                        ShowSubtitle('Score : ' .. Score, 2000)
                        SendNUIMessage({
                            type = 'ding'
                        })
                    end
                    DeletePed(NPC)
                end
            else
                FreezeEntityPosition(PlayerPedId(), false)
                InAim = true
                exitTextShown = false
                SetNuiFocus(true, true)
                ShowSubtitle('', 0)
                SendNUIMessage({
                    type = 'score',
                    score = Score
                })
                -- Log score to webhook
                TriggerServerEvent('aim:logScore', 'NPC Roll', Score)
                RemoveWeaponFromPed(PlayerPedId(), GetHashKey('WEAPON_x'))
                RemoveWeaponFromPed(PlayerPedId(), GetHashKey('WEAPON_x'))
                RemoveBlip(blip)
                Citizen.Wait(2800)
                SendNUIMessage({
                    type = 'ui'
                })
                SetEntityAlpha(PlayerPedId(), 255)
                reset = false
                break
            end
        end
    end)
end

-- Add key binding for E to exit
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if InAim and exitTextShown then
            if IsControlJustPressed(0, 38) then -- E key
                reset = true
            end
        end
    end
end)

-- Create permanent blip for the aim training location
Citizen.CreateThread(function()
    local blip = AddBlipForCoord(Config.StandingPosition.x, Config.StandingPosition.y, Config.StandingPosition.z)
    SetBlipSprite(blip, 313) -- Shooting range blip
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 1.0)
    SetBlipColour(blip, 1) -- Red
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Aim Training")
    EndTextCommandSetBlipName(blip)
end)

-- Check if player is too far from training area and reset if needed
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        if InAim and exitTextShown then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - Config.StandingPosition)
            
            if distance > 50.0 then -- If player is more than 50 units away
                reset = true
                ShowSubtitle("You moved too far from the training area!", 3000)
            end
        end
    end
end)

-- Add help text when player is near the training area but not in training
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0)
        if not InAim then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - Config.StandingPosition)
            
            if distance < 10.0 then
                DrawMarker(1, Config.StandingPosition.x, Config.StandingPosition.y, Config.StandingPosition.z - 1.0, 
                    0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 
                    1.0, 1.0, 1.0, 255, 0, 0, 100, 
                    false, true, 2, false, nil, nil, false)
                
                if distance < 3.0 then
                    SetTextComponentFormat("STRING")
                    AddTextComponentString("Press ~INPUT_CONTEXT~ to start aim training or type /" .. Config.CommandName)
                    DisplayHelpTextFromStringLabel(0, 0, 1, -1)
                    
                    if IsControlJustPressed(0, 38) then -- E key
                        ExecuteCommand(Config.CommandName)
                    end
                end
            end
        end
    end
end)




