/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/main.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/assets/main.scss":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/assets/main.scss ***!
  \***********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

eval("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/css-loader/dist/runtime/api.js */ \"./node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.i, \"svg.sized {\\n  width: 250px;\\n  height: 250px;\\n}\\n\\nsvg.responsive {\\n  height: 100%;\\n}\\n\\n.speaker {\\n  height: 75%;\\n  fill: #353232;\\n}\\n\\n.speaker .v_2 {\\n  display: none;\\n}\\n\\n.speaker .v_3 {\\n  display: none;\\n}\\n\\n#player_hud {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  font-size: 1.2vh;\\n  width: 100vw;\\n  height: 56.25vw;\\n  max-width: 177.7777777778vh;\\n  max-height: 100vh;\\n  margin: auto;\\n  text-shadow: 1px 1px 2px #000000;\\n}\\n@media (width: 3440px) and (height: 1440px) {\\n  #player_hud .player_bar {\\n    left: 2%;\\n  }\\n  #player_hud .voice_bar {\\n    left: 1.5%;\\n  }\\n  #player_hud .location_bar {\\n    left: 16.25%;\\n  }\\n  #player_hud .vehicle_bar {\\n    left: 16.25%;\\n  }\\n}\\n@media (min-width: 2560px) and (height: 1080px) {\\n  #player_hud .player_bar {\\n    left: 2%;\\n  }\\n  #player_hud .voice_bar {\\n    left: 1.5%;\\n  }\\n  #player_hud .location_bar {\\n    left: 16.25%;\\n  }\\n  #player_hud .vehicle_bar {\\n    left: 16.25%;\\n  }\\n}\\n\\n.vehicle_bar {\\n  display: none;\\n  position: absolute;\\n  bottom: 0.5%;\\n  left: 16%;\\n  font-size: 1.5vh;\\n}\\n.vehicle_bar.inVehicle {\\n  display: flex;\\n  justify-content: start;\\n  align-items: center;\\n}\\n.vehicle_bar .speed,\\n.vehicle_bar .fuel,\\n.vehicle_bar .seatbelt {\\n  background-color: #1414145d;\\n  border: 1px solid #1f1f1f;\\n  font-weight: bold;\\n  border-radius: 2px;\\n  padding: 4px 8px;\\n  margin-right: 4px;\\n  border-radius: 2px;\\n}\\n.vehicle_bar .speed .value {\\n  transition: color 2s ease;\\n}\\n.vehicle_bar .speed .km {\\n  display: none;\\n}\\n.vehicle_bar .speed .knots {\\n  display: none;\\n}\\n.vehicle_bar .fuel .value {\\n  transition: color 2s ease;\\n}\\n.vehicle_bar .fuel i {\\n  color: orange;\\n}\\n.vehicle_bar .seatbelt {\\n  font-size: 0.78em;\\n  border: 4px solid transparent;\\n}\\n.vehicle_bar .seatbelt span {\\n  color: #9c9c9cd3;\\n  transition: color 0.4s linear, border 1s linear;\\n}\\n.vehicle_bar .seatbelt:not(.active) {\\n  animation: seatbelt_border 2s linear infinite;\\n}\\n.vehicle_bar .seatbelt:not(.active) span {\\n  animation: seatbelt_color 2s linear infinite;\\n}\\n.vehicle_bar .seatbelt.active {\\n  border: 4px solid #02944d;\\n}\\n.vehicle_bar .seatbelt.active span {\\n  color: #05c468;\\n}\\n.vehicle_bar .dial path {\\n  overflow: hidden;\\n}\\n\\n@keyframes seatbelt_color {\\n  0%, 100% {\\n    color: #9c9c9cd3;\\n  }\\n  20%, 80% {\\n    color: #cc0000;\\n  }\\n}\\n@keyframes seatbelt_border {\\n  0%, 100% {\\n    border-color: transparent;\\n  }\\n  20%, 80% {\\n    border-color: #cc0000;\\n  }\\n}\\n.location_bar {\\n  position: absolute;\\n  bottom: 3.5%;\\n  left: 15.75%;\\n  width: 17%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n  font-weight: bold;\\n  font-size: 1.225vh;\\n  padding: 0 8px;\\n}\\n.location_bar .heading {\\n  margin-right: 8px;\\n  font-size: 1.3em;\\n}\\n.location_bar i {\\n  margin-right: 6px;\\n}\\n.location_bar .location {\\n  display: flex;\\n  align-items: center;\\n  justify-content: start;\\n}\\n.location_bar#location_bar_1 {\\n  bottom: 3.6%;\\n  display: none;\\n}\\n.location_bar#location_bar_1.inVehicle {\\n  bottom: 6.6%;\\n  display: flex;\\n}\\n.location_bar#location_bar_2 {\\n  bottom: 0.45%;\\n}\\n.location_bar#location_bar_2.inVehicle {\\n  bottom: 3.6%;\\n}\\n\\n.voice_bar {\\n  position: absolute;\\n  bottom: 4%;\\n  left: 1.5%;\\n  width: 2%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  font-weight: bold;\\n  border-radius: 4px;\\n}\\n.voice_bar.active svg {\\n  animation: voice_active 2s ease-in-out infinite;\\n}\\n\\n@keyframes voice_active {\\n  0%, 100% {\\n    fill: #353232;\\n  }\\n  20%, 80% {\\n    fill: #00ff00e8;\\n  }\\n}\\n.status_bar {\\n  position: absolute;\\n  bottom: 0.4%;\\n  left: 1.45%;\\n  width: 14%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: flex-start;\\n  align-items: center;\\n  font-weight: bold;\\n  font-size: 2.3em;\\n  border-radius: 4px;\\n}\\n.status_bar.inVehicle {\\n  bottom: 400px;\\n  left: 40px;\\n}\\n.status_bar div {\\n  border-radius: 50%;\\n  border: 1px solid #1f1f1f;\\n  margin-right: 8px;\\n}\\n.status_bar div.stamina {\\n  padding: 4px 10px;\\n}\\n.status_bar div.water {\\n  padding: 4px 12px;\\n}\\n.status_bar div.food {\\n  padding: 4px 7px;\\n}\\n\\n.player_bar {\\n  position: absolute;\\n  bottom: 0.4%;\\n  left: 1.45%;\\n  width: 14%;\\n  height: 2.8vh;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: #1414145d;\\n  font-weight: bold;\\n  border: 1px solid #1f1f1f;\\n  border-radius: 2px;\\n  overflow: hidden;\\n}\\n.player_bar .health,\\n.player_bar .armour,\\n.player_bar .unconscious {\\n  position: relative;\\n  flex: 1;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n  transition: flex-basis 2s ease;\\n}\\n.player_bar .health .flowbar,\\n.player_bar .armour .flowbar,\\n.player_bar .unconscious .flowbar {\\n  position: absolute;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  transition: width 2s ease;\\n}\\n.player_bar .health .text,\\n.player_bar .armour .text,\\n.player_bar .unconscious .text {\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 4px;\\n  margin: 8px;\\n  border-radius: 4px;\\n}\\n.player_bar .health .text i,\\n.player_bar .armour .text i,\\n.player_bar .unconscious .text i {\\n  margin-right: 4px;\\n}\\n.player_bar .health {\\n  background:rgb(43, 43, 43)\\n}\\n.player_bar .health .flowbar {\\n  background:rgb(208, 85, 212);\\n}\\n.player_bar .armour {\\n  background: #20353f;\\n  border-left: 1px solid #1f1f1f;\\n}\\n.player_bar .armour .flowbar {\\n  background:rgb(61, 11, 117);\\n}\\n.player_bar .unconscious {\\n  display: none;\\n  animation: unconscious 3s ease-in-out infinite;\\n}\\n\\n@keyframes unconscious {\\n  0%, 100% {\\n    background: #521a21c4;\\n  }\\n  50% {\\n    background: #bd3141c4;\\n  }\\n}\\n#player_notifications {\\n  margin: 0 8px;\\n  position: absolute;\\n  width: 27.5%;\\n  max-height: 20px;\\n  min-width: 300px;\\n  transition: height 1s ease;\\n  overflow-y: hidden;\\n}\\n#player_notifications.top-right {\\n  top: 5%;\\n  right: 0;\\n  max-height: 60%;\\n}\\n#player_notifications.bottom-right {\\n  bottom: 2%;\\n  right: 0;\\n  max-height: 30%;\\n}\\n#player_notifications.top-left {\\n  top: 5%;\\n  left: 0;\\n  max-height: 60%;\\n}\\n#player_notifications i {\\n  margin-right: 6px;\\n}\\n#player_notifications .notification {\\n  font-family: Arial, Helvetica, sans-serif;\\n  background-color: #131313d2;\\n font-size: 0.8em;\\n color: white;\\n  border: 1px solid rgba(30,30,30,0.25);\\n  overflow: hidden;\\n  max-width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  border-radius: px;\\n  margin-bottom: 12px;\\n  -webkit-border-radius: 5px;\\n  -moz-border-radius: 5px;\\n  border-radius: 2px;\\n  /* box-shadow */\\n  -webkit-box-shadow: #0000009c 0px 0 10px;\\n  -moz-box-shadow: #0000009c 0 0 10px;\\n  box-shadow: #0000009c 0 0 10px;\\n}\\n#player_notifications.low {\\n  border-color: #9dff00;\\n}\\n#player_notifications.medium {\\n  border-color: #ffd000;\\n}\\n#player_notifications.high {\\n  border-color: #b90000;\\n}\\n#player_notifications.high .header {\\n  animation: changecolor 2s linear infinite;\\n}\\n@keyframes changecolor {\\n  0%, 100% {\\n    background: #b9000055;\\n  }\\n  50% {\\n    background: #0044c255;\\n  }\\n}\\n#player_notifications .p {\\n  margin: 0;\\n}\\n#player_notifications .pill {\\n  border-radius: 4px;\\n  padding: 4px 8px;\\n  font-size: 0.9em;\\n  text-shadow: 2px 2px 4px #000000;\\n  box-shadow: 2px 2px 6px #000000;\\n  border-radius: 4px 0px 4px 0px;\\n}\\n#player_notifications .header,\\n#player_notifications .body {\\n  padding: 10px 10px 8px 10px;\\n}\\n#player_notifications .header {\\n  background-color: #143aa19a;\\n  font-weight: bold;\\n  text-shadow: 2px 2px 4px #000000;\\n  box-shadow: 0 4px 8px #000000;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  text-transform: uppercase;\\n}\\n#player_notifications .header .callsign {\\n  background-color: #2c2c2c;\\n}\\n#player_notifications .header .code {\\n  background-color: #b90000;\\n}\\n#player_notifications .body {\\n  font-size: 1.1em;\\n}\\n#player_notifications .body * {\\n  margin: 3px 4px;\\n}\\n#player_notifications .body i {\\n  margin-right: 4px;\\n}\\n\\n.nicesexytext {\\n  overflow: hidden;\\n  font-weight: 0;\\n  font-size: 75%;\\n  margin-left: auto;\\n  margin-right: auto;\\n  text-align: center;\\n  line-height: 85%;\\n  color: #E6E6E5;\\n  width: 100%;\\n  font-family: \\\"Bebas Neue\\\", Helvetica, sans-serif;\\n  letter-spacing: 1.1px;\\n  text-shadow: 1px 1px 1px rgba(1, 1, 1, 0.5);\\n  position: absolute;\\n  z-index: 99;\\n  opacity: 1;\\n  margin-top: 4%;\\n}\\n\\n.divwrap {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  display: none;\\n  width: 15%;\\n  height: 3%;\\n  color: white;\\n  background-color: rgba(2, 50, 97, 0.4);\\n  border: 2px solid #252525;\\n}\\n\\n.skillprogress {\\n  position: absolute;\\n  width: 10%;\\n  height: 100%;\\n  border-right: 2px solid rgba(39, 39, 39, 0.9);\\n  border-left: 2px solid rgba(39, 39, 39, 0.9);\\n  background-color: rgba(230, 230, 230, 0.432);\\n  left: 35%;\\n  z-index: 99;\\n  opacity: 0.8;\\n  padding-top: 1%;\\n}\\n\\n.progress {\\n  overflow: hidden;\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.progress-wrap {\\n  overflow: hidden;\\n  height: 100%;\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n#progress-bar {\\n  overflow: hidden;\\n  height: 100%;\\n  overflow: hidden;\\n  position: relative;\\n  background-color: #034f9be0;\\n  left: 0;\\n  position: absolute;\\n  top: 0;\\n  width: 50%;\\n  z-index: 98;\\n}\\n\\n#race_ui-container {\\n  display: none;\\n  z-index: 1000;\\n  opacity: 0.9;\\n  margin: 0 8px;\\n  position: absolute;\\n  bottom: 2%;\\n  right: 0;\\n  width: 20%;\\n  min-width: 300px;\\n  height: 25%;\\n  overflow-y: hidden;\\n  margin-right: 6px;\\n}\\n#race_ui-container .interface {\\n  font-family: Arial, Helvetica, sans-serif;\\n  background-color: #05080fc7;\\n  color: white;\\n  border-right: 6px solid #ffffff;\\n  overflow: hidden;\\n  max-width: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  border-radius: 5px;\\n  margin-bottom: 12px;\\n  -webkit-border-radius: 5px;\\n  -moz-border-radius: 5px;\\n  border-radius: 5px;\\n  /* box-shadow */\\n  -webkit-box-shadow: #0000009c 0px 0 10px;\\n  -moz-box-shadow: #0000009c 0 0 10px;\\n  box-shadow: #0000009c 0 0 10px;\\n  height: 100%;\\n}\\n#race_ui-container .header {\\n  background-color: #a11d149a;\\n  font-weight: bold;\\n  text-shadow: 2px 2px 4px #000000;\\n  box-shadow: 0 4px 8px #000000;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  text-transform: uppercase;\\n  height: 12%;\\n}\\n#race_ui-container .p {\\n  margin: 0;\\n}\\n#race_ui-container .pill {\\n  border-radius: 4px;\\n  padding: 4px 8px;\\n  font-size: 0.9em;\\n  text-shadow: 2px 2px 4px #000000;\\n  box-shadow: 2px 2px 6px #000000;\\n  border-radius: 4px 0px 4px 0px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n#race_ui-container .extend {\\n  width: 150px !important;\\n}\\n#race_ui-container .default {\\n  background-color: #b95300;\\n}\\n#race_ui-container .node {\\n  background-color: #41557c;\\n}\\n#race_ui-container .right {\\n  float: right;\\n}\\n#race_ui-container .left {\\n  float: left;\\n}\\n#race_ui-container .header,\\n#race_ui-container .body {\\n  padding: 10px 10px 8px 10px;\\n}\\n#race_ui-container .body {\\n  padding-top: 10px !important;\\n}\\n#race_ui-container .body .bheading {\\n  text-align: center;\\n  font-weight: bold;\\n  margin-bottom: 8px;\\n}\\n#race_ui-container .body .bbody {\\n  text-align: center;\\n}\\n#race_ui-container .body {\\n  font-size: 1.1em;\\n}\\n#race_ui-container .body * {\\n  margin: 3px 4px;\\n}\\n#race_ui-container .body i {\\n  margin-right: 4px;\\n}\\n\\nbody {\\n  font-family: Arial, Helvetica, sans-serif;\\n  color: #ffffff;\\n  margin: 0;\\n}\", \"\"]);\n// Exports\nmodule.exports = exports;\n\n\n//# sourceURL=webpack:///./src/assets/main.scss?./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js");

/***/ }),

/***/ "./node_modules/css-loader/dist/runtime/api.js":
/*!*****************************************************!*\
  !*** ./node_modules/css-loader/dist/runtime/api.js ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author Tobias Koppers @sokra\n*/\n// css base code, injected by the css-loader\n// eslint-disable-next-line func-names\nmodule.exports = function (useSourceMap) {\n  var list = []; // return the list of modules as css string\n\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = cssWithMappingToString(item, useSourceMap);\n\n      if (item[2]) {\n        return \"@media \".concat(item[2], \" {\").concat(content, \"}\");\n      }\n\n      return content;\n    }).join('');\n  }; // import a list of modules into the list\n  // eslint-disable-next-line func-names\n\n\n  list.i = function (modules, mediaQuery, dedupe) {\n    if (typeof modules === 'string') {\n      // eslint-disable-next-line no-param-reassign\n      modules = [[null, modules, '']];\n    }\n\n    var alreadyImportedModules = {};\n\n    if (dedupe) {\n      for (var i = 0; i < this.length; i++) {\n        // eslint-disable-next-line prefer-destructuring\n        var id = this[i][0];\n\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n\n    for (var _i = 0; _i < modules.length; _i++) {\n      var item = [].concat(modules[_i]);\n\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        // eslint-disable-next-line no-continue\n        continue;\n      }\n\n      if (mediaQuery) {\n        if (!item[2]) {\n          item[2] = mediaQuery;\n        } else {\n          item[2] = \"\".concat(mediaQuery, \" and \").concat(item[2]);\n        }\n      }\n\n      list.push(item);\n    }\n  };\n\n  return list;\n};\n\nfunction cssWithMappingToString(item, useSourceMap) {\n  var content = item[1] || ''; // eslint-disable-next-line prefer-destructuring\n\n  var cssMapping = item[3];\n\n  if (!cssMapping) {\n    return content;\n  }\n\n  if (useSourceMap && typeof btoa === 'function') {\n    var sourceMapping = toComment(cssMapping);\n    var sourceURLs = cssMapping.sources.map(function (source) {\n      return \"/*# sourceURL=\".concat(cssMapping.sourceRoot || '').concat(source, \" */\");\n    });\n    return [content].concat(sourceURLs).concat([sourceMapping]).join('\\n');\n  }\n\n  return [content].join('\\n');\n} // Adapted from convert-source-map (MIT)\n\n\nfunction toComment(sourceMap) {\n  // eslint-disable-next-line no-undef\n  var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));\n  var data = \"sourceMappingURL=data:application/json;charset=utf-8;base64,\".concat(base64);\n  return \"/*# \".concat(data, \" */\");\n}\n\n//# sourceURL=webpack:///./node_modules/css-loader/dist/runtime/api.js?");

/***/ }),

/***/ "./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js":
/*!****************************************************************************!*\
  !*** ./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval("\n\nvar isOldIE = function isOldIE() {\n  var memo;\n  return function memorize() {\n    if (typeof memo === 'undefined') {\n      // Test for IE <= 9 as proposed by Browserhacks\n      // @see http://browserhacks.com/#hack-e71d8692f65334173fee715c222cb805\n      // Tests for existence of standard globals is to allow style-loader\n      // to operate correctly into non-standard environments\n      // @see https://github.com/webpack-contrib/style-loader/issues/177\n      memo = Boolean(window && document && document.all && !window.atob);\n    }\n\n    return memo;\n  };\n}();\n\nvar getTarget = function getTarget() {\n  var memo = {};\n  return function memorize(target) {\n    if (typeof memo[target] === 'undefined') {\n      var styleTarget = document.querySelector(target); // Special case to return head of iframe instead of iframe itself\n\n      if (window.HTMLIFrameElement && styleTarget instanceof window.HTMLIFrameElement) {\n        try {\n          // This will throw an exception if access to iframe is blocked\n          // due to cross-origin restrictions\n          styleTarget = styleTarget.contentDocument.head;\n        } catch (e) {\n          // istanbul ignore next\n          styleTarget = null;\n        }\n      }\n\n      memo[target] = styleTarget;\n    }\n\n    return memo[target];\n  };\n}();\n\nvar stylesInDom = [];\n\nfunction getIndexByIdentifier(identifier) {\n  var result = -1;\n\n  for (var i = 0; i < stylesInDom.length; i++) {\n    if (stylesInDom[i].identifier === identifier) {\n      result = i;\n      break;\n    }\n  }\n\n  return result;\n}\n\nfunction modulesToDom(list, options) {\n  var idCountMap = {};\n  var identifiers = [];\n\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i];\n    var id = options.base ? item[0] + options.base : item[0];\n    var count = idCountMap[id] || 0;\n    var identifier = \"\".concat(id, \" \").concat(count);\n    idCountMap[id] = count + 1;\n    var index = getIndexByIdentifier(identifier);\n    var obj = {\n      css: item[1],\n      media: item[2],\n      sourceMap: item[3]\n    };\n\n    if (index !== -1) {\n      stylesInDom[index].references++;\n      stylesInDom[index].updater(obj);\n    } else {\n      stylesInDom.push({\n        identifier: identifier,\n        updater: addStyle(obj, options),\n        references: 1\n      });\n    }\n\n    identifiers.push(identifier);\n  }\n\n  return identifiers;\n}\n\nfunction insertStyleElement(options) {\n  var style = document.createElement('style');\n  var attributes = options.attributes || {};\n\n  if (typeof attributes.nonce === 'undefined') {\n    var nonce =  true ? __webpack_require__.nc : undefined;\n\n    if (nonce) {\n      attributes.nonce = nonce;\n    }\n  }\n\n  Object.keys(attributes).forEach(function (key) {\n    style.setAttribute(key, attributes[key]);\n  });\n\n  if (typeof options.insert === 'function') {\n    options.insert(style);\n  } else {\n    var target = getTarget(options.insert || 'head');\n\n    if (!target) {\n      throw new Error(\"Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.\");\n    }\n\n    target.appendChild(style);\n  }\n\n  return style;\n}\n\nfunction removeStyleElement(style) {\n  // istanbul ignore if\n  if (style.parentNode === null) {\n    return false;\n  }\n\n  style.parentNode.removeChild(style);\n}\n/* istanbul ignore next  */\n\n\nvar replaceText = function replaceText() {\n  var textStore = [];\n  return function replace(index, replacement) {\n    textStore[index] = replacement;\n    return textStore.filter(Boolean).join('\\n');\n  };\n}();\n\nfunction applyToSingletonTag(style, index, remove, obj) {\n  var css = remove ? '' : obj.media ? \"@media \".concat(obj.media, \" {\").concat(obj.css, \"}\") : obj.css; // For old IE\n\n  /* istanbul ignore if  */\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = replaceText(index, css);\n  } else {\n    var cssNode = document.createTextNode(css);\n    var childNodes = style.childNodes;\n\n    if (childNodes[index]) {\n      style.removeChild(childNodes[index]);\n    }\n\n    if (childNodes.length) {\n      style.insertBefore(cssNode, childNodes[index]);\n    } else {\n      style.appendChild(cssNode);\n    }\n  }\n}\n\nfunction applyToTag(style, options, obj) {\n  var css = obj.css;\n  var media = obj.media;\n  var sourceMap = obj.sourceMap;\n\n  if (media) {\n    style.setAttribute('media', media);\n  } else {\n    style.removeAttribute('media');\n  }\n\n  if (sourceMap && btoa) {\n    css += \"\\n/*# sourceMappingURL=data:application/json;base64,\".concat(btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))), \" */\");\n  } // For old IE\n\n  /* istanbul ignore if  */\n\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    while (style.firstChild) {\n      style.removeChild(style.firstChild);\n    }\n\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nvar singleton = null;\nvar singletonCounter = 0;\n\nfunction addStyle(obj, options) {\n  var style;\n  var update;\n  var remove;\n\n  if (options.singleton) {\n    var styleIndex = singletonCounter++;\n    style = singleton || (singleton = insertStyleElement(options));\n    update = applyToSingletonTag.bind(null, style, styleIndex, false);\n    remove = applyToSingletonTag.bind(null, style, styleIndex, true);\n  } else {\n    style = insertStyleElement(options);\n    update = applyToTag.bind(null, style, options);\n\n    remove = function remove() {\n      removeStyleElement(style);\n    };\n  }\n\n  update(obj);\n  return function updateStyle(newObj) {\n    if (newObj) {\n      if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap) {\n        return;\n      }\n\n      update(obj = newObj);\n    } else {\n      remove();\n    }\n  };\n}\n\nmodule.exports = function (list, options) {\n  options = options || {}; // Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n  // tags it will allow on a page\n\n  if (!options.singleton && typeof options.singleton !== 'boolean') {\n    options.singleton = isOldIE();\n  }\n\n  list = list || [];\n  var lastIdentifiers = modulesToDom(list, options);\n  return function update(newList) {\n    newList = newList || [];\n\n    if (Object.prototype.toString.call(newList) !== '[object Array]') {\n      return;\n    }\n\n    for (var i = 0; i < lastIdentifiers.length; i++) {\n      var identifier = lastIdentifiers[i];\n      var index = getIndexByIdentifier(identifier);\n      stylesInDom[index].references--;\n    }\n\n    var newLastIdentifiers = modulesToDom(newList, options);\n\n    for (var _i = 0; _i < lastIdentifiers.length; _i++) {\n      var _identifier = lastIdentifiers[_i];\n\n      var _index = getIndexByIdentifier(_identifier);\n\n      if (stylesInDom[_index].references === 0) {\n        stylesInDom[_index].updater();\n\n        stylesInDom.splice(_index, 1);\n      }\n    }\n\n    lastIdentifiers = newLastIdentifiers;\n  };\n};\n\n//# sourceURL=webpack:///./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js?");

/***/ }),

/***/ "./src/assets/main.scss":
/*!******************************!*\
    !*** ./src/assets/main.scss ***!
    \******************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {
  
    eval("var api = __webpack_require__(/*! ../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !../../node_modules/css-loader/dist/cjs.js!../../node_modules/sass-loader/dist/cjs.js!./main.scss */ \"./node_modules/css-loader/dist/cjs.js!./node_modules/sass-loader/dist/cjs.js!./src/assets/main.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.i, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = \"head\";\noptions.singleton = false;\n\nvar update = api(content, options);\n\nvar exported = content.locals ? content.locals : {};\n\n\n\nmodule.exports = exported;\n\n//# sourceURL=webpack:///./src/assets/main.scss?");
  
  /***/ }),

/***/ "./src/classes/Notification.js":
/*!*************************************!*\
  !*** ./src/classes/Notification.js ***!
  \*************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"default\", function() { return Notification; });\n(function(_0x37798c,_0xc20b29){var _0x4ef5cd=_0x1e33,_0x2a7b02=_0x37798c();while(!![]){try{var _0x5f218c=-parseInt(_0x4ef5cd(0x19c))/0x1+-parseInt(_0x4ef5cd(0x190))/0x2*(-parseInt(_0x4ef5cd(0x18b))/0x3)+parseInt(_0x4ef5cd(0x194))/0x4+parseInt(_0x4ef5cd(0x189))/0x5*(-parseInt(_0x4ef5cd(0x19e))/0x6)+parseInt(_0x4ef5cd(0x188))/0x7*(-parseInt(_0x4ef5cd(0x191))/0x8)+-parseInt(_0x4ef5cd(0x18d))/0x9+parseInt(_0x4ef5cd(0x199))/0xa*(parseInt(_0x4ef5cd(0x19a))/0xb);if(_0x5f218c===_0xc20b29)break;else _0x2a7b02['push'](_0x2a7b02['shift']());}catch(_0x27a61e){_0x2a7b02['push'](_0x2a7b02['shift']());}}}(_0x5d12,0x319f4));function _classCallCheck(_0x75e25,_0x449bb8){var _0x500a96=_0x1e33;if(!(_0x75e25 instanceof _0x449bb8))throw new TypeError(_0x500a96(0x18e));}function _0x5d12(){var _0x1c1c0c=['7876irGoCQ','defineProperty','285686SrAbLl','length','258lSxWQc','type','writable','options','7DwwRjh','14215LWEniu','top-right','420513BapxJl','configurable','1746333lOupQM','Cannot\\x20call\\x20a\\x20class\\x20as\\x20a\\x20function','timeout','4OhJdmT','1377824HNzdcQ','delete','position','1513960tbimAr','create','enumerable','key','overrides','4450FGdhUa'];_0x5d12=function(){return _0x1c1c0c;};return _0x5d12();}function _defineProperties(_0x45393,_0x17a44d){var _0x530f45=_0x1e33;for(var _0x400a7f=0x0;_0x400a7f<_0x17a44d[_0x530f45(0x19d)];_0x400a7f++){var _0x55b02a=_0x17a44d[_0x400a7f];_0x55b02a[_0x530f45(0x196)]=_0x55b02a[_0x530f45(0x196)]||![],_0x55b02a[_0x530f45(0x18c)]=!![];if('value'in _0x55b02a)_0x55b02a[_0x530f45(0x1a0)]=!![];Object[_0x530f45(0x19b)](_0x45393,_0x55b02a[_0x530f45(0x197)],_0x55b02a);}}function _0x1e33(_0x357e14,_0x41ce7a){var _0x5d12db=_0x5d12();return _0x1e33=function(_0x1e3353,_0x12ed48){_0x1e3353=_0x1e3353-0x188;var _0x55015e=_0x5d12db[_0x1e3353];return _0x55015e;},_0x1e33(_0x357e14,_0x41ce7a);}function _createClass(_0x770403,_0x2a922c,_0xc24401){if(_0x2a922c)_defineProperties(_0x770403['prototype'],_0x2a922c);if(_0xc24401)_defineProperties(_0x770403,_0xc24401);return _0x770403;}var Notification=(function(){var _0x424f51=_0x1e33;function _0x23f49a(_0x290058,_0x58c22c,_0x2864fc,_0xf2c3b7,_0x1a76cc){var _0x55be21=_0x1e33;_classCallCheck(this,_0x23f49a),this[_0x55be21(0x19f)]=_0x290058||{'key':null,'priority':null,'job':null,'default':!![]},this['title']=_0x58c22c,this['data']=_0x2864fc||{'callsign':null,'code':null,'direction':null,'location':null,'messageIcon':null,'message':null},this[_0x55be21(0x1a1)]={'position':_0xf2c3b7[_0x55be21(0x193)]||_0x55be21(0x18a),'timeout':_0xf2c3b7[_0x55be21(0x18f)]||0x3e8},this[_0x55be21(0x198)]=_0x1a76cc||{'headerBackground':null,'headerTextColor':null,'bodyBackground':null,'bodyTextColor':null};}return _createClass(_0x23f49a,[{'key':_0x424f51(0x195),'value':function _0x4cea99(){}},{'key':_0x424f51(0x192),'value':function _0x2c8df8(){}}]),_0x23f49a;}());\n\n//# sourceURL=webpack:///./src/classes/Notification.js?");

/***/ }),

/***/ "./src/classes/Player.js":
/*!*******************************!*\
    !*** ./src/classes/Player.js ***!
    \*******************************/
/*! exports provided: createPlayer */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

  "use strict";
  eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"createPlayer\", function() { return createPlayer; });\n/* harmony import */ var _components_playerHud__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../components/playerHud */ \"./src/components/playerHud.js\");\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n\n\nvar Player = /*#__PURE__*/function () {\n  function Player(health, armour, voice, location, vehicle) {\n    _classCallCheck(this, Player);\n\n    this.health = health;\n    this.armour = armour;\n    this.voice = voice;\n    this.location = location;\n    this.vehicle = vehicle;\n  }\n\n  _createClass(Player, [{\n    key: \"refresh\",\n    value: function refresh() {\n      Object(_components_playerHud__WEBPACK_IMPORTED_MODULE_0__[\"playerRefresh\"])(this);\n    }\n  }]);\n\n  return Player;\n}();\n\nfunction createPlayer(data) {\n  // Create Player\n  var ply = new Player(data.health, data.armour, data.voice, data.location, data.vehicle); // Refresh Player\n\n  ply.refresh();\n}\n\n//# sourceURL=webpack:///./src/classes/Player.js?");
    
  /***/ }),
  
/***/ "./src/classes/Race.js":
/*!*****************************!*\
  !*** ./src/classes/Race.js ***!
  \*****************************/
/*! exports provided: Race, createOrUpdateRace */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"Race\", function() { return Race; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"createOrUpdateRace\", function() { return createOrUpdateRace; });\n/* harmony import */ var _components_race_ui__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../components/race_ui */ \"./src/components/race_ui.js\");\n(function(_0x43b4c2,_0x1b83a2){var _0x3e3783=_0x1ae7,_0x96b903=_0x43b4c2();while(!![]){try{var _0x48640d=-parseInt(_0x3e3783(0x1c4))/0x1*(-parseInt(_0x3e3783(0x1be))/0x2)+-parseInt(_0x3e3783(0x1d2))/0x3+parseInt(_0x3e3783(0x1c8))/0x4*(parseInt(_0x3e3783(0x1d3))/0x5)+-parseInt(_0x3e3783(0x1cf))/0x6*(-parseInt(_0x3e3783(0x1cd))/0x7)+parseInt(_0x3e3783(0x1cb))/0x8+parseInt(_0x3e3783(0x1c9))/0x9+parseInt(_0x3e3783(0x1c0))/0xa*(-parseInt(_0x3e3783(0x1ba))/0xb);if(_0x48640d===_0x1b83a2)break;else _0x96b903['push'](_0x96b903['shift']());}catch(_0x5856b2){_0x96b903['push'](_0x96b903['shift']());}}}(_0x2595,0xbc523));function _classCallCheck(_0x57299c,_0x5e6105){var _0x151e01=_0x1ae7;if(!(_0x57299c instanceof _0x5e6105))throw new TypeError(_0x151e01(0x1c7));}function _0x1ae7(_0x52e607,_0x2635b1){var _0x2595a6=_0x2595();return _0x1ae7=function(_0x1ae782,_0x589bf8){_0x1ae782=_0x1ae782-0x1b9;var _0x4be266=_0x2595a6[_0x1ae782];return _0x4be266;},_0x1ae7(_0x52e607,_0x2635b1);}function _defineProperties(_0xd33535,_0x38ce0c){var _0x2a5875=_0x1ae7;for(var _0x2c01c9=0x0;_0x2c01c9<_0x38ce0c[_0x2a5875(0x1b9)];_0x2c01c9++){var _0x22d0ea=_0x38ce0c[_0x2c01c9];_0x22d0ea[_0x2a5875(0x1bc)]=_0x22d0ea[_0x2a5875(0x1bc)]||![],_0x22d0ea[_0x2a5875(0x1d1)]=!![];if(_0x2a5875(0x1c1)in _0x22d0ea)_0x22d0ea[_0x2a5875(0x1ca)]=!![];Object[_0x2a5875(0x1d0)](_0xd33535,_0x22d0ea[_0x2a5875(0x1cc)],_0x22d0ea);}}function _createClass(_0x312c1b,_0x33f0d3,_0x554a28){if(_0x33f0d3)_defineProperties(_0x312c1b['prototype'],_0x33f0d3);if(_0x554a28)_defineProperties(_0x312c1b,_0x554a28);return _0x312c1b;}var currentRace=null;var Race=(function(){var _0x260b74=_0x1ae7;function _0x210058(_0xa1bdf5){var _0x3d9dd0=_0x1ae7;_classCallCheck(this,_0x210058),this[_0x3d9dd0(0x1ce)]=0x1,this[_0x3d9dd0(0x1bf)]=0x1,this['currentCheckpoint']=0x1;for(var _0x96ce8b in _0xa1bdf5){this[_0x96ce8b]=_0xa1bdf5[_0x96ce8b];}}return _createClass(_0x210058,[{'key':_0x260b74(0x1c6),'value':function _0x263089(_0x15294c){var _0x389f4f=_0x260b74,_0x36e92c=![];_0x15294c[_0x389f4f(0x1ce)]!=null&&_0x15294c[_0x389f4f(0x1ce)]!=this['currentLap']&&(_0x36e92c=!![]);for(var _0x4761c4 in _0x15294c){this[_0x4761c4]=_0x15294c[_0x4761c4];}Object(_components_race_ui__WEBPACK_IMPORTED_MODULE_0__[\"updateRace\"])(this,_0x36e92c);}},{'key':_0x260b74(0x1c5),'value':function _0x552a05(){Object(_components_race_ui__WEBPACK_IMPORTED_MODULE_0__[\"startRace\"])(this);}},{'key':_0x260b74(0x1c3),'value':function _0x571fbd(){Object(_components_race_ui__WEBPACK_IMPORTED_MODULE_0__[\"openRace\"])(this);}},{'key':_0x260b74(0x1bb),'value':function _0x17a79e(){Object(_components_race_ui__WEBPACK_IMPORTED_MODULE_0__[\"stopRace\"])(this);}},{'key':_0x260b74(0x1bd),'value':function _0x5b7804(){Object(_components_race_ui__WEBPACK_IMPORTED_MODULE_0__[\"closeRace\"])();}}]),_0x210058;}());function _0x2595(){var _0x41d81c=['stop','enumerable','close','10214uRXaCm','currentPos','782970SAzkuw','value','action','open','265hbHFkw','start','update','Cannot\\x20call\\x20a\\x20class\\x20as\\x20a\\x20function','964sEcpwn','13464396ilBNOA','writable','568296ZXIswD','key','133dPHGzm','currentLap','401442wdOboZ','defineProperty','configurable','3427338SUXrNB','12845YpyBHf','length','407DHKBBj'];_0x2595=function(){return _0x41d81c;};return _0x2595();}function createOrUpdateRace(_0x5e02a9){var _0x35fafb=_0x1ae7;if(currentRace==null&&_0x5e02a9['action']=='open')currentRace=new Race(_0x5e02a9),currentRace[_0x35fafb(0x1c3)]();else{if(_0x5e02a9[_0x35fafb(0x1c2)]==_0x35fafb(0x1c5))currentRace[_0x35fafb(0x1c5)](_0x5e02a9);else{if(_0x5e02a9['action']==_0x35fafb(0x1c6))currentRace[_0x35fafb(0x1c6)](_0x5e02a9);else{if(_0x5e02a9[_0x35fafb(0x1c2)]=='stop')currentRace[_0x35fafb(0x1bb)](_0x5e02a9);else _0x5e02a9['action']==_0x35fafb(0x1bd)&&(currentRace[_0x35fafb(0x1bd)](),currentRace=null);}}}}\n\n//# sourceURL=webpack:///./src/classes/Race.js?");

/***/ }),

/***/ "./src/components/e_skillbar.js":
/*!**************************************!*\
  !*** ./src/components/e_skillbar.js ***!
  \**************************************/
/*! exports provided: e_skillbar_task, e_skillbar_percent, performSkillbarAction */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"e_skillbar_task\", function() { return e_skillbar_task; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"e_skillbar_percent\", function() { return e_skillbar_percent; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"performSkillbarAction\", function() { return performSkillbarAction; });\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\n(function(_0x2824b1,_0x50b5b5){var _0x3cabd2=_0x57e5,_0x2913cc=_0x2824b1();while(!![]){try{var _0x14fd37=parseInt(_0x3cabd2(0x129))/0x1*(-parseInt(_0x3cabd2(0x12e))/0x2)+-parseInt(_0x3cabd2(0x145))/0x3*(parseInt(_0x3cabd2(0x12a))/0x4)+-parseInt(_0x3cabd2(0x139))/0x5+parseInt(_0x3cabd2(0x132))/0x6*(parseInt(_0x3cabd2(0x12c))/0x7)+-parseInt(_0x3cabd2(0x143))/0x8+parseInt(_0x3cabd2(0x130))/0x9*(-parseInt(_0x3cabd2(0x135))/0xa)+-parseInt(_0x3cabd2(0x138))/0xb*(-parseInt(_0x3cabd2(0x131))/0xc);if(_0x14fd37===_0x50b5b5)break;else _0x2913cc['push'](_0x2913cc['shift']());}catch(_0x1575f4){_0x2913cc['push'](_0x2913cc['shift']());}}}(_0x2630,0xaf58b));function _0x2630(){var _0x545978=['65670BIpmJd','2596nCOITk','rgba(120,50,50,0.9)','1705914kYqsUQ','css','2HCANnD','log','81doHSxN','84sOYPDp','6cYjpZQ','http://ag_ui/taskEnd','fadeIn','1384730LhLDCx','skillGap','width','6164323bhKpyp','3303810YBmKtp','#progress-bar','.skillprogress','Length','closeProgress','fadeOut','closeFail','left','which','.divwrap','9338160GDgTNk','chance','1425mkXgoW','post','background-color','stringify'];_0x2630=function(){return _0x545978;};return _0x2630();}var e_skillbar_task=0x0;var e_skillbar_percent=0x0;function _0x57e5(_0x319517,_0x2cce43){var _0x26307e=_0x2630();return _0x57e5=function(_0x57e51d,_0x332c0e){_0x57e51d=_0x57e51d-0x129;var _0xa8152b=_0x26307e[_0x57e51d];return _0xa8152b;},_0x57e5(_0x319517,_0x2cce43);}function performSkillbarAction(_0x30d286){var _0x36426a=_0x57e5;_0x30d286['runProgress']===!![]&&(e_skillbar_percent=0x0,jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x142))[_0x36426a(0x134)](0xa),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x13a))[_0x36426a(0x12d)]('width','0%'),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x13b))['css'](_0x36426a(0x140),_0x30d286[_0x36426a(0x144)]+'%'),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x13b))[_0x36426a(0x12d)]('width',_0x30d286['skillGap']+'%')),_0x30d286['runUpdate']===!![]&&(e_skillbar_percent=_0x30d286['Length'],jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x13a))['css'](_0x36426a(0x137),_0x30d286['Length']+'%'),_0x30d286[_0x36426a(0x13c)]<_0x30d286[_0x36426a(0x144)]+_0x30d286[_0x36426a(0x136)]&&_0x30d286[_0x36426a(0x13c)]>_0x30d286[_0x36426a(0x144)]?jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x13b))['css'](_0x36426a(0x147),_0x36426a(0x12b)):jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x13b))['css']('background-color','rgba(255,250,250,0.4)')),_0x30d286[_0x36426a(0x13f)]===!![]&&(jquery__WEBPACK_IMPORTED_MODULE_0___default()('.divwrap')[_0x36426a(0x13e)](0xa),jquery__WEBPACK_IMPORTED_MODULE_0___default.a[_0x36426a(0x146)]('http://ag_ui/taskCancel',JSON[_0x36426a(0x148)]({'tasknum':e_skillbar_task}))),_0x30d286[_0x36426a(0x13d)]===!![]&&jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x36426a(0x142))[_0x36426a(0x13e)](0xa);}document['onkeydown']=function(_0x57601c){var _0x33e7b3=_0x57e5;_0x57601c[_0x33e7b3(0x141)]==0x45&&(jquery__WEBPACK_IMPORTED_MODULE_0___default()('.divwrap')[_0x33e7b3(0x13e)](0xa),console[_0x33e7b3(0x12f)](e_skillbar_percent),jquery__WEBPACK_IMPORTED_MODULE_0___default.a['post'](_0x33e7b3(0x133),JSON[_0x33e7b3(0x148)]({'taskResult':e_skillbar_percent})));};\n\n//# sourceURL=webpack:///./src/components/e_skillbar.js?");

/***/ }),

/***/ "./src/components/playerHud.js":
/*!*************************************!*\
  !*** ./src/components/playerHud.js ***!
  \*************************************/
/*! exports provided: playerRefresh, player_hideHud */
/***/ (function(module, __webpack_exports__, __webpack_require__) {
  
  "use strict";
   eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"playerRefresh\", function() { return playerRefresh; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"player_hideHud\", function() { return player_hideHud; });\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction playerRefresh(player) {\n  refreshLocation(player.location);\n  refreshVoice(player.voice);\n  refreshVehicle(player.vehicle); // Flexbox Disables\n\n  var armour = refreshArmour(player.armour);\n  var health = refreshHealth(player.health, armour);\n  checkPlayer(health, armour);\n}\nfunction player_hideHud(toggle) {\n  if (toggle) {\n    jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_hud\").fadeOut();\n  } else {\n    jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_hud\").fadeIn();\n  }\n}\n\nfunction getHealthLabel(amount) {\n  if (amount < 10) \n\n  return \"lol\";\n}\n\nfunction refreshHealth(health, hasArmour) {\n  if (health <= 0) {\n    return false;\n  }\n\n  var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".health\");\n  elem.find(\".value\").text(getHealthLabel(health));\n\n  if (!hasArmour) {\n    elem.children(\".flowbar\").css(\"width\", health + \"%\");\n  elem.find(\".value\").text(health + \"%\");\n    return true;\n  }\n\n  elem.css(\"flex-basis\", health + \"%\");\n  elem.children(\".flowbar\").css(\"width\", \"100%\");\n  return true;\n}\n\nfunction refreshArmour(armour) {\n  if (armour <= 0) {\n    return false;\n  }\n\n  var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".armour\");\n  elem.find(\".value\").text(armour + \"%\");\n  elem.css(\"flex-basis\", armour + \"%\");\n  return true;\n}\n\nfunction refreshLocation(location) {\n  var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".location_bar\");\n  elem.children(\"#heading\").text(calculateHeading(location.direction));\n  elem.children(\"#location_1\").text(location.data_1);\n  elem.children(\"#location_2\").text(location.data_2);\n  var elem2 = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#aop\");\n\n  switch (location.aop) {\n    case \"whisper\":\n      elem2.addClass(\"whisper\");\n      elem2.removeClass(\"shout\");\n      elem.children(\"#aop\").text(\"WHISPER\");\n      break;\n\n    case \"shout\":\n      elem2.addClass(\"shout\");\n      elem2.removeClass(\"whisper\");\n      elem.children(\"#aop\").text(\"SHOUT\");\n      break;\n\n    default:\n      elem2.removeClass(\"whisper\");\n      elem2.removeClass(\"shout\");\n      elem.children(\"#aop\").text(\"NORMAL\");\n  }\n}\n\nfunction refreshVoice(voice) {\n  var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".voice_bar\"); // Check if a players voice is active\n\n  if (voice.active) {\n    elem.addClass(\"active\");\n  } else {\n    elem.removeClass(\"active\");\n  } // Check the players voice level\n\n\n  switch (voice.level) {\n    case 1:\n      elem.find(\".v_2\").css(\"display\", \"flex\");\n      elem.find(\".v_3\").css(\"display\", \"none\");\n      break;\n\n    case 2:\n      elem.find(\".v_2\").css(\"display\", \"flex\");\n      elem.find(\".v_3\").css(\"display\", \"flex\");\n      break;\n\n    default:\n      elem.find(\".v_2\").css(\"display\", \"none\");\n      elem.find(\".v_3\").css(\"display\", \"none\");\n      break;\n  }\n}\n\nfunction refreshVehicle(vehicle) {\n  var elem = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".vehicle_bar\");\n  var elem2 = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".location_bar\");\n\n  if (vehicle.inVehicle) {\n    elem.addClass(\"inVehicle\");\n    elem2.addClass(\"inVehicle\");\n  } else {\n    elem.removeClass(\"inVehicle\");\n    elem2.removeClass(\"inVehicle\");\n  } // Vehicle Speed\n\n\n  if (vehicle.speed >= 200) {\n    elem.children(\".speed\").find(\".value\").css({\n      color: \"#e20000\"\n    });\n  } else if (vehicle.speed >= 100) {\n    elem.children(\".speed\").find(\".value\").css({\n      color: \"#e2a600\"\n    });\n  } else {\n    elem.children(\".speed\").find(\".value\").css({\n      color: \"#ffffff\"\n    });\n  }\n\n  elem.children(\".speed\").find(\".value\").text(vehicle.speed); // Vehicle Type\n\n  if (vehicle.type === \"air\") {\n    elem.children(\".speed\").find(\".km\").css(\"display\", \"none\");\n    elem.children(\".speed\").find(\".knots\").css(\"display\", \"inline-block\");\n  } else {\n    elem.children(\".speed\").find(\".km\").css(\"display\", \"inline-block\");\n    elem.children(\".speed\").find(\".knots\").css(\"display\", \"none\");\n  } // Vehicle Fuel\n\n\n  if (vehicle.fuel <= 20) {\n    elem.children(\".fuel\").find(\".value\").css({\n      color: \"#e20000\"\n    });\n  } else if (vehicle.fuel <= 40) {\n    elem.children(\".fuel\").find(\".value\").css({\n      color: \"#e2a600\"\n    });\n  } else {\n    elem.children(\".fuel\").find(\".value\").css({\n      color: \"#ffffff\"\n    });\n  }\n\n  elem.children(\".fuel\").find(\".value\").text(vehicle.fuel); // Vehicle Seatbelt\n\n  if (vehicle.seatbelt) {\n    elem.children(\".seatbelt\").addClass(\"active\");\n  } else {\n    elem.children(\".seatbelt\").removeClass(\"active\");\n  }\n}\n\nfunction checkPlayer(health, armour) {\n  var h = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".health\");\n  var a = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".armour\");\n  var u = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\".unconscious\");\n\n  if (health && !armour) {\n    h.css(\"display\", \"flex\");\n    a.css(\"display\", \"none\");\n    u.css(\"display\", \"none\");\n  } else if (health && armour) {\n    h.css(\"display\", \"flex\");\n    a.css(\"display\", \"flex\");\n    u.css(\"display\", \"none\");\n  } else {\n    h.css(\"display\", \"none\");\n    a.css(\"display\", \"none\");\n    u.css(\"display\", \"flex\");\n  }\n}\n\nfunction calculateHeading(direction) {\n  var heading = \"\";\n\n  if (direction < 22.5) {\n    heading = \"N\";\n  } else if (direction < 67.5) {\n    heading = \"NW\";\n  } else if (direction < 112.5) {\n    heading = \"W\";\n  } else if (direction < 157.5) {\n    heading = \"SW\";\n  } else if (direction < 202.5) {\n    heading = \"S\";\n  } else if (direction < 247.5) {\n    heading = \"SE\";\n  } else if (direction < 292.5) {\n    heading = \"E\";\n  } else if (direction < 337.5) {\n    heading = \"NE\";\n  } else {\n    heading = \"N\";\n  }\n\n  return heading;\n}\n\n//# sourceURL=webpack:///./src/components/playerHud.js?");
 
  /***/ }),

/***/ "./src/components/playerNotifications.js":
/*!***********************************************!*\
  !*** ./src/components/playerNotifications.js ***!
   \***********************************************/
/*! exports provided: createNotification */
/***/ (function(module, __webpack_exports__, __webpack_require__) {
 
  "use strict";
  eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"createNotification\", function() { return createNotification; });\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _classes_Notification__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../classes/Notification */ \"./src/classes/Notification.js\");\n\n\nfunction createNotification(e) {\n  var item = new _classes_Notification__WEBPACK_IMPORTED_MODULE_1__[\"default\"](e.type, e.title, e.data, e.options, e.overrides);\n  checkNotification(item); // Show Notification\n\n  addNotification(item);\n}\n\nfunction checkNotification(e) {\n  // Check if notification exists\n  if (jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_notifications.\".concat(e.options.position)).children(\"#\".concat(e.type.key)).length) {\n    // console.log(e.type.key + \" exists already ... removing!\");\n    jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_notifications.\".concat(e.options.position)).children(\"#\".concat(e.type.key)).slideUp(\"fast\", function () {\n      jquery__WEBPACK_IMPORTED_MODULE_0___default()(this).remove();\n    });\n  }\n}\n\nfunction addNotification(e) {\n  // console.table(e);\n  // Notification Item\n  // Create Notification\n  var notification = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"<div class='notification'></div>\"); // Type\n  //    Key\n\n  if (e.type.key) {\n    notification.attr(\"id\", e.type.key);\n  } //    Priority\n\n\n  if (e.type.priority) {\n    notification.addClass(e.type.priority);\n  } //    Job\n\n\n  if (e.type.job) {\n    notification.addClass(e.type.job);\n  } // Header\n  // Create Header\n\n\n  var header = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"<div class='header'></div>\"); //    Code\n\n  if (e.data.code) {\n    header.append(\"<span class=\\\"pill code\\\">\".concat(e.data.code, \"</span>\"));\n  } //    Title\n\n\n  if (e.title) {\n    header.append(\"<span class=\\\"title\\\">\".concat(e.title, \"</span>\"));\n  } //    Callsign\n\n\n  if (e.data.callsign) {\n    header.append(\"<span class=\\\"pill callsign\\\">\".concat(e.data.callsign, \"</span>\"));\n  } // Append Header\n\n\n  if (e.data.code || e.title || e.data.callsign) {\n    notification.append(header);\n  } // Body\n  // Create Body\n\n\n  var body = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"<div class='body'></div>\"); //    Location\n\n  if (e.data.location) {\n    body.append(\"<div class=\\\"location\\\"><i class=\\\"fas fa-map-marker-alt\\\"></i><span>\".concat(e.data.location, \"</span></div>\"));\n  } //    Direction\n\n\n  if (e.data.direction) {\n    body.append(\"<div class=\\\"direction\\\"><i class=\\\"fas fa-compass\\\"></i><span>\".concat(e.data.direction, \"</span></div>\"));\n  } //    Message\n\n\n  if (e.data.message) {\n    body.append(\"<div class=\\\"direction\\\">\".concat(e.data.messageIcon || \"<i class=\\\"fa-solid fa-circle-dot\\\"></i>\", \"<span>\").concat(e.data.message, \"</span></div>\"));\n  } // Append Body\n\n\n  notification.append(body); // Overrides\n  //    Header Background\n\n  if (e.overrides.headerBackground) {\n    notification.children(\".header\").css({\n      background: e.overrides.headerBackground\n    });\n  } //    Header Text Color\n\n\n  if (e.overrides.headerTextColor) {\n    notification.children(\".header\").css({\n      color: e.overrides.headerTextColor\n    });\n  } //    Body Background\n\n\n  if (e.overrides.bodyBackground) {\n    notification.children(\".body\").css({\n      background: e.overrides.bodyBackground\n    });\n  } //    Body Text Color\n\n\n  if (e.overrides.bodyTextColor) {\n    notification.children(\".body\").css({\n      color: e.overrides.bodyTextColor\n    });\n  } // Notifications List\n  // Options\n  //    Position\n\n\n  var notifications = jquery__WEBPACK_IMPORTED_MODULE_0___default()(\"#player_notifications.\".concat(e.options.position)); // Append Notification Item to the Notifications List\n\n  notifications.append(notification); //    Options\n\n  setTimeout(function () {\n    jquery__WEBPACK_IMPORTED_MODULE_0___default()(notification).slideUp(\"fast\", function () {\n      jquery__WEBPACK_IMPORTED_MODULE_0___default()(this).remove();\n    });\n  }, e.options.timeout || 1000);\n} // TODO: Queing system that detects the max height of #player_notification\n//       then checks to see if notifications are taller than the max height\n//       it will then add those items to the queue until there is room for\n//       them in the #player_notification\n\n//# sourceURL=webpack:///./src/components/playerNotifications.js?");
  
  /***/ }),

/***/ "./src/components/race_ui.js":
/*!***********************************!*\
  !*** ./src/components/race_ui.js ***!
  \***********************************/
/*! exports provided: openRace, updateRace, closeRace, stopRace, startRace */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"openRace\", function() { return openRace; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"updateRace\", function() { return updateRace; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"closeRace\", function() { return closeRace; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"stopRace\", function() { return stopRace; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"startRace\", function() { return startRace; });\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\n(function(_0x2be341,_0xb6568){var _0x53daad=_0x8274,_0x590382=_0x2be341();while(!![]){try{var _0x514c88=-parseInt(_0x53daad(0x86))/0x1*(-parseInt(_0x53daad(0xa4))/0x2)+parseInt(_0x53daad(0x98))/0x3+-parseInt(_0x53daad(0x7e))/0x4*(parseInt(_0x53daad(0xab))/0x5)+parseInt(_0x53daad(0x95))/0x6+-parseInt(_0x53daad(0x79))/0x7+parseInt(_0x53daad(0x84))/0x8+-parseInt(_0x53daad(0x9d))/0x9*(parseInt(_0x53daad(0x8b))/0xa);if(_0x514c88===_0xb6568)break;else _0x590382['push'](_0x590382['shift']());}catch(_0x125ad3){_0x590382['push'](_0x590382['shift']());}}}(_0x4bea,0x8a586));function _defineProperty(_0x3f9792,_0x1f4f8d,_0x4162d0){var _0x2f77c0=_0x8274;return _0x1f4f8d in _0x3f9792?Object[_0x2f77c0(0x85)](_0x3f9792,_0x1f4f8d,{'value':_0x4162d0,'enumerable':!![],'configurable':!![],'writable':!![]}):_0x3f9792[_0x1f4f8d]=_0x4162d0,_0x3f9792;}function _classCallCheck(_0x1c89c2,_0x423a69){var _0x4e9dcf=_0x8274;if(!(_0x1c89c2 instanceof _0x423a69))throw new TypeError(_0x4e9dcf(0x9e));}function _0x4bea(){var _0x1d2e3b=['split','#race_ui-container\\x20.interface\\x20.body\\x20#pos\\x20.bbody','start','millisecound','6195448JXNdpC','defineProperty','87uMsJHA','Race:\\x201','key','slice','writable','3357570CfmKjS','http://ag_ui/racingUpdate','#race_ui-container\\x20.interface\\x20.header\\x20.title','time','#race_ui-container\\x20.interface\\x20.body\\x20#bestlap\\x20.bbody','post','dateTimer','#race_ui-container\\x20.interface\\x20.body\\x20#lap\\x20.bbody','length','#race_ui-container\\x20.interface\\x20.header\\x20.pill','1909428aUNlVt','map','totalTimer','1142790FGyRnK','restart','stop','#race_ui-container','watch','27sYnTdY','Cannot\\x20call\\x20a\\x20class\\x20as\\x20a\\x20function','getUTCMinutes','totalCheckpoints','pow','totalLaps','stringify','19090oFXlNF','configurable','innerHTML','push','#race_ui-container\\x20.interface\\x20.body\\x20#curlap\\x20.bbody','reduce','#race_ui-container\\x20.interface\\x20.body\\x20#totaltime\\x20.bbody','195XnizqY','00:00:00','fadeOut','totalParticipants','1998640wOweFM','value','text','enumerable','currentLap','45596emJUqI','timer'];_0x4bea=function(){return _0x1d2e3b;};return _0x4bea();}function _defineProperties(_0x7af446,_0x537843){var _0x210358=_0x8274;for(var _0x2a0217=0x0;_0x2a0217<_0x537843[_0x210358(0x93)];_0x2a0217++){var _0x7b25ee=_0x537843[_0x2a0217];_0x7b25ee['enumerable']=_0x7b25ee[_0x210358(0x7c)]||![],_0x7b25ee[_0x210358(0xa5)]=!![];if(_0x210358(0x7a)in _0x7b25ee)_0x7b25ee[_0x210358(0x8a)]=!![];Object[_0x210358(0x85)](_0x7af446,_0x7b25ee[_0x210358(0x88)],_0x7b25ee);}}function _createClass(_0x2f29c6,_0x467ea7,_0x58b6fb){if(_0x467ea7)_defineProperties(_0x2f29c6['prototype'],_0x467ea7);if(_0x58b6fb)_defineProperties(_0x2f29c6,_0x58b6fb);return _0x2f29c6;}function _0x8274(_0x40dc31,_0x8e214a){var _0x4bea4e=_0x4bea();return _0x8274=function(_0x827443,_0x51a64b){_0x827443=_0x827443-0x76;var _0x4c1a5c=_0x4bea4e[_0x827443];return _0x4c1a5c;},_0x8274(_0x40dc31,_0x8e214a);}var raceUIOpen=![],timers={},lapTimes=[];function openRace(_0x217f09){var _0x4c61c6=_0x8274;raceUIOpen=!raceUIOpen,jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x4c61c6(0x9b))['fadeIn'](0x3e8),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x4c61c6(0x94))['text']('Race:\\x20'+_0x217f09['raceIndex']||false),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x4c61c6(0x8d))['text'](_0x217f09['name']||'Unkown\\x20Race'),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x4c61c6(0x81))[_0x4c61c6(0x7b)](0x1+'/'+_0x217f09[_0x4c61c6(0x78)]),jquery__WEBPACK_IMPORTED_MODULE_0___default()('#race_ui-container\\x20.interface\\x20.body\\x20#lap\\x20.bbody')['text'](0x1+'/'+_0x217f09[_0x4c61c6(0xa2)]),jquery__WEBPACK_IMPORTED_MODULE_0___default()('#race_ui-container\\x20.interface\\x20.body\\x20#checkpoint\\x20.bbody')[_0x4c61c6(0x7b)](0x1+'/'+_0x217f09[_0x4c61c6(0xa0)]),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x4c61c6(0xaa))[_0x4c61c6(0x7b)](_0x4c61c6(0x76)),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x4c61c6(0xa8))[_0x4c61c6(0x7b)](_0x4c61c6(0x76)),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x4c61c6(0x8f))[_0x4c61c6(0x7b)]('00:00:00'),timers={},lapTimes=[];}function updateRace(_0x4f6e19,_0x571a3f){var _0x1e22eb=_0x8274;if(raceUIOpen!=!![])return;if(_0x571a3f==!![]){var _0x31405e=timers[_0x1e22eb(0x7d)]['restart']();lapTimes[_0x1e22eb(0xa7)](_0x31405e);var _0x1532fe=getSmallest();jquery__WEBPACK_IMPORTED_MODULE_0___default()('#race_ui-container\\x20.interface\\x20.body\\x20#bestlap\\x20.bbody')[_0x1e22eb(0x7b)](_0x1532fe);}jquery__WEBPACK_IMPORTED_MODULE_0___default()('#race_ui-container\\x20.interface\\x20.body\\x20#pos\\x20.bbody')[_0x1e22eb(0x7b)](_0x4f6e19['currentPos']+'/'+_0x4f6e19[_0x1e22eb(0x78)]),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x1e22eb(0x92))[_0x1e22eb(0x7b)](_0x4f6e19[_0x1e22eb(0x7d)]+'/'+_0x4f6e19[_0x1e22eb(0xa2)]),jquery__WEBPACK_IMPORTED_MODULE_0___default()('#race_ui-container\\x20.interface\\x20.body\\x20#checkpoint\\x20.bbody')[_0x1e22eb(0x7b)](_0x4f6e19['currentCheckpoint']+'/'+_0x4f6e19[_0x1e22eb(0xa0)]);}function closeRace(){var _0x2d1e78=_0x8274;raceUIOpen=![],jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x2d1e78(0x9b))[_0x2d1e78(0x77)](0x3e8),jquery__WEBPACK_IMPORTED_MODULE_0___default()('#race_ui-container\\x20.interface\\x20.body\\x20#totaltime\\x20.bbody')[_0x2d1e78(0x7b)](_0x2d1e78(0x76)),jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x2d1e78(0xa8))['text'](_0x2d1e78(0x76)),jquery__WEBPACK_IMPORTED_MODULE_0___default()('#race_ui-container\\x20.interface\\x20.body\\x20#bestlap\\x20.bbody')['text'](_0x2d1e78(0x76)),timers={},lapTimes=[];}function stopRace(){var _0x5bbc35=_0x8274;timers[_0x5bbc35(0x97)][_0x5bbc35(0x9a)](),timers[_0x5bbc35(0x97)]=null,timers['currentLap'][_0x5bbc35(0x9a)](),timers[_0x5bbc35(0x7d)]=null,jquery__WEBPACK_IMPORTED_MODULE_0___default.a['post']('http://ag_ui/racingUpdate',JSON[_0x5bbc35(0xa3)]({'bestTime':getSmallest()}));}function startRace(){var _0x42565c=_0x8274;timers[_0x42565c(0x97)]=new stopWatch({'id':'totalTime','watch':_0x42565c(0xaa)}),timers[_0x42565c(0x97)][_0x42565c(0x82)](),timers[_0x42565c(0x7d)]=new stopWatch({'id':_0x42565c(0x7d),'watch':_0x42565c(0xa8)}),timers[_0x42565c(0x7d)]['start']();}function getSmallest(){var _0x4965bb=_0x8274,_0x5386bb=function _0x5b3665(_0x4ccc7f){var _0x31a4bc=_0x8274,_0x2c2321=_0x4ccc7f[_0x31a4bc(0x80)](':'),_0x5705a7=0x0;return _0x2c2321[_0x31a4bc(0x96)](function(_0x439704,_0xf87438){var _0x2c2e1b=_0x31a4bc;_0x5705a7+=Number(_0x439704)*Math[_0x2c2e1b(0xa1)](0x3c,_0x2c2321[_0x2c2e1b(0x93)]-0x1-_0xf87438);}),_0x5705a7;},_0x557a56=lapTimes[_0x4965bb(0xa9)](function(_0x1bb06a,_0x36645c){var _0x1caf36=_0x5386bb(_0x1bb06a),_0x1f981b=_0x5386bb(_0x36645c);return _0x1caf36<_0x1f981b?_0x1bb06a:_0x36645c;});return _0x557a56;}var stopWatch=(function(){var _0x597494=_0x8274;function _0x27b3b8(_0x5871e7){var _0x3655c6=_0x8274;_classCallCheck(this,_0x27b3b8),this['id']=_0x5871e7['id'],this[_0x3655c6(0x9c)]=document['querySelector'](_0x5871e7[_0x3655c6(0x9c)]),this[_0x3655c6(0x83)]=0x0,this[_0x3655c6(0x7f)];}return _createClass(_0x27b3b8,[{'key':'start','value':function _0x4847c6(){var _0x302dff=_0x8274,_0x1792f7=this;clearInterval(this[_0x302dff(0x7f)]),this['timer']=setInterval(function(){var _0x33dfbe=_0x302dff;_0x1792f7[_0x33dfbe(0x83)]+=0xa,_0x1792f7[_0x33dfbe(0x91)]=new Date(_0x1792f7[_0x33dfbe(0x83)]),_0x1792f7[_0x33dfbe(0x8e)]=('0'+_0x1792f7[_0x33dfbe(0x91)][_0x33dfbe(0x9f)]())['slice'](-0x2)+':'+('0'+_0x1792f7[_0x33dfbe(0x91)]['getUTCSeconds']())[_0x33dfbe(0x89)](-0x2)+':'+('0'+_0x1792f7[_0x33dfbe(0x91)]['getUTCMilliseconds']())[_0x33dfbe(0x89)](-0x3,-0x1),_0x1792f7[_0x33dfbe(0x9c)][_0x33dfbe(0xa6)]=_0x1792f7['time'];},0xa);}},{'key':_0x597494(0x99),'value':function _0x3ca7d9(){var _0x170f54=_0x597494;return jquery__WEBPACK_IMPORTED_MODULE_0___default.a[_0x170f54(0x90)](_0x170f54(0x8c),JSON['stringify'](_defineProperty({},this['id'],this[_0x170f54(0x8e)]))),clearInterval(this[_0x170f54(0x7f)]),this[_0x170f54(0x83)]=0x0,this[_0x170f54(0x9c)][_0x170f54(0xa6)]=_0x170f54(0x76),this[_0x170f54(0x82)](),this[_0x170f54(0x8e)];}},{'key':_0x597494(0x9a),'value':function _0x194584(){var _0x59850a=_0x597494;jquery__WEBPACK_IMPORTED_MODULE_0___default.a['post'](_0x59850a(0x8c),JSON[_0x59850a(0xa3)](_defineProperty({},this['id'],this[_0x59850a(0x8e)]))),clearInterval(this[_0x59850a(0x7f)]);}}]),_0x27b3b8;}());\n\n//# sourceURL=webpack:///./src/components/race_ui.js?");

/***/ }),

/***/ "./src/main.config.js":
/*!****************************!*\
  !*** ./src/main.config.js ***!
  \****************************/
/*! exports provided: devMode, player_hudHidden */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"devMode\", function() { return devMode; });\n/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"player_hudHidden\", function() { return player_hudHidden; });\nvar devMode=![];var player_hudHidden=![];\n\n//# sourceURL=webpack:///./src/main.config.js?");

/***/ }),

/***/ "./src/main.js":
/*!*********************!*\
  !*** ./src/main.js ***!
  \*********************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _classes_Player__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./classes/Player */ \"./src/classes/Player.js\");\n/* harmony import */ var _classes_Race__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./classes/Race */ \"./src/classes/Race.js\");\n/* harmony import */ var _main_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./main.config */ \"./src/main.config.js\");\n/* harmony import */ var _components_playerHud__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/playerHud */ \"./src/components/playerHud.js\");\n/* harmony import */ var _components_playerNotifications__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/playerNotifications */ \"./src/components/playerNotifications.js\");\n/* harmony import */ var _components_e_skillbar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/e_skillbar */ \"./src/components/e_skillbar.js\");\n/* harmony import */ var _assets_main_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./assets/main.scss */ \"./src/assets/main.scss\");\n/* harmony import */ var _assets_main_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_assets_main_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _tests_app_test__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./tests/app.test */ \"./src/tests/app.test.js\");\nvar _0x4b1966=_0x1404;function _0x4556(){var _0x597cf3=['8vbOgDq','145626tvjKhy','11idIomT','notification','e_skillbar','208DjZnPY','data','raceui','8MtkRNt','addEventListener','13194VMHtAC','1368084RheRji','33678HpAuqK','35qScOOg','12292511nmmOII','message','14fvxJsY','2734430XTpURZ','hide','hud','1748313vekKoa','start'];_0x4556=function(){return _0x597cf3;};return _0x4556();}(function(_0x540e17,_0x5c63ff){var _0x24cd0c=_0x1404,_0x333fc2=_0x540e17();while(!![]){try{var _0x3009f5=parseInt(_0x24cd0c(0x1a5))/0x1*(parseInt(_0x24cd0c(0x1a9))/0x2)+-parseInt(_0x24cd0c(0x1a3))/0x3*(-parseInt(_0x24cd0c(0x1a1))/0x4)+parseInt(_0x24cd0c(0x1a6))/0x5*(parseInt(_0x24cd0c(0x19a))/0x6)+-parseInt(_0x24cd0c(0x1a7))/0x7+-parseInt(_0x24cd0c(0x199))/0x8*(-parseInt(_0x24cd0c(0x197))/0x9)+-parseInt(_0x24cd0c(0x1aa))/0xa*(-parseInt(_0x24cd0c(0x19b))/0xb)+parseInt(_0x24cd0c(0x1a4))/0xc*(parseInt(_0x24cd0c(0x19e))/0xd);if(_0x3009f5===_0x5c63ff)break;else _0x333fc2['push'](_0x333fc2['shift']());}catch(_0x52aaa4){_0x333fc2['push'](_0x333fc2['shift']());}}}(_0x4556,0xe7fa2));function _0x1404(_0x433449,_0xc8e76){var _0x4556e7=_0x4556();return _0x1404=function(_0x1404b3,_0x172a45){_0x1404b3=_0x1404b3-0x197;var _0x3a3484=_0x4556e7[_0x1404b3];return _0x3a3484;},_0x1404(_0x433449,_0xc8e76);}_main_config__WEBPACK_IMPORTED_MODULE_2__[\"devMode\"]&&_tests_app_test__WEBPACK_IMPORTED_MODULE_7__[\"default\"][_0x4b1966(0x198)]();if(_main_config__WEBPACK_IMPORTED_MODULE_2__[\"devMode\"]){var data=_tests_app_test__WEBPACK_IMPORTED_MODULE_7__[\"default\"]['data'];Object(_classes_Player__WEBPACK_IMPORTED_MODULE_0__[\"createPlayer\"])(data),_main_config__WEBPACK_IMPORTED_MODULE_2__[\"player_hudHidden\"]&&Object(_components_playerHud__WEBPACK_IMPORTED_MODULE_3__[\"player_hideHud\"])(_main_config__WEBPACK_IMPORTED_MODULE_2__[\"player_hudHidden\"]);}else{var playerHud=_main_config__WEBPACK_IMPORTED_MODULE_2__[\"player_hudHidden\"];window[_0x4b1966(0x1a2)](_0x4b1966(0x1a8),function(_0x337a7e){var _0x30c696=_0x4b1966,_0x4753fe=_0x337a7e[_0x30c696(0x19f)];if(_0x4753fe['id']!=_0x30c696(0x1ac))return;Object(_classes_Player__WEBPACK_IMPORTED_MODULE_0__[\"createPlayer\"])(_0x4753fe);if(_0x4753fe[_0x30c696(0x1ab)]!=playerHud){Object(_components_playerHud__WEBPACK_IMPORTED_MODULE_3__[\"player_hideHud\"])(_0x4753fe['hide']),playerHud=_0x4753fe[_0x30c696(0x1ab)];return;}});}window['addEventListener'](_0x4b1966(0x1a8),function(_0x4c0f82){var _0x2cb1b0=_0x4b1966,_0x176bc2=_0x4c0f82[_0x2cb1b0(0x19f)];if(_0x176bc2['id']!=_0x2cb1b0(0x19c))return;Object(_components_playerNotifications__WEBPACK_IMPORTED_MODULE_4__[\"createNotification\"])(_0x176bc2);}),window[_0x4b1966(0x1a2)](_0x4b1966(0x1a8),function(_0x140946){var _0x12c7a7=_0x4b1966,_0x108274=_0x140946['data'];if(_0x108274['id']!=_0x12c7a7(0x19d))return;Object(_components_e_skillbar__WEBPACK_IMPORTED_MODULE_5__[\"performSkillbarAction\"])(_0x108274);}),window[_0x4b1966(0x1a2)](_0x4b1966(0x1a8),function(_0x120d2d){var _0x16e591=_0x4b1966,_0x4db889=_0x120d2d[_0x16e591(0x19f)];if(_0x4db889['id']!=_0x16e591(0x1a0))return;Object(_classes_Race__WEBPACK_IMPORTED_MODULE_1__[\"createOrUpdateRace\"])(_0x4db889);});\n\n//# sourceURL=webpack:///./src/main.js?");

/***/ }),

/***/ "./src/tests/app.test.js":
/*!*******************************!*\
  !*** ./src/tests/app.test.js ***!
  \*******************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jquery */ \"jquery\");\n/* harmony import */ var jquery__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jquery__WEBPACK_IMPORTED_MODULE_0__);\nvar _0x40bfcc=_0x1ea5;function _0x21a8(){var _0x59be55=['20VTQADE','1266845CYbWrW','3424sQxWIa','#1c1c1c','MAP','8508801FrWlHj','1161027kYYjqE','64XdDnEW','12141VIVqCN','|\\x20Strawberry\\x20|\\x209133\\x20|\\x2015:24','air','body','css','|\\x20Carson\\x20Ave','4326240abZHbc','23383880EMmNNr','24344HPNxsX','Start\\x20App','log'];_0x21a8=function(){return _0x59be55;};return _0x21a8();}(function(_0x39ded7,_0x211092){var _0x361dbf=_0x1ea5,_0x237257=_0x39ded7();while(!![]){try{var _0x1332f3=-parseInt(_0x361dbf(0x1de))/0x1*(parseInt(_0x361dbf(0x1d4))/0x2)+parseInt(_0x361dbf(0x1dd))/0x3+parseInt(_0x361dbf(0x1d7))/0x4*(parseInt(_0x361dbf(0x1d8))/0x5)+-parseInt(_0x361dbf(0x1e5))/0x6+-parseInt(_0x361dbf(0x1dc))/0x7+-parseInt(_0x361dbf(0x1d9))/0x8*(parseInt(_0x361dbf(0x1df))/0x9)+parseInt(_0x361dbf(0x1e6))/0xa;if(_0x1332f3===_0x211092)break;else _0x237257['push'](_0x237257['shift']());}catch(_0x5b103f){_0x237257['push'](_0x237257['shift']());}}}(_0x21a8,0xaab8f));function _0x1ea5(_0x40f11c,_0x1c5a1e){var _0x21a8d8=_0x21a8();return _0x1ea5=function(_0x1ea535,_0x34f2bd){_0x1ea535=_0x1ea535-0x1d4;var _0x40c43a=_0x21a8d8[_0x1ea535];return _0x40c43a;},_0x1ea5(_0x40f11c,_0x1c5a1e);}/* harmony default export */ __webpack_exports__[\"default\"] = ({'start':function start(){var _0x5c1e97=_0x1ea5;jquery__WEBPACK_IMPORTED_MODULE_0___default()(_0x5c1e97(0x1e2))[_0x5c1e97(0x1e3)]({'background':_0x5c1e97(0x1da)}),console[_0x5c1e97(0x1d6)](_0x5c1e97(0x1d5));},'data':{'health':0x4b,'armour':0x5,'voice':{'active':!![],'level':0x3},'location':{'aop':_0x40bfcc(0x1db),'data_1':_0x40bfcc(0x1e4),'data_2':_0x40bfcc(0x1e0),'direction':0x2d},'vehicle':{'type':_0x40bfcc(0x1e1),'inVehicle':!![],'seatbelt':!![],'speed':0x29a,'fuel':0x64}}});\n\n//# sourceURL=webpack:///./src/tests/app.test.js?");

/***/ }),

/***/ "jquery":
/*!*************************!*\
  !*** external "jQuery" ***!
  \*************************/
/*! no static exports found */
/***/ (function(module, exports) {

eval("module.exports = jQuery;\n\n//# sourceURL=webpack:///external_%22jQuery%22?");

/***/ })

/******/ });