resource_manifest_version '44febabe-d386-4d18-afbe-5e627f4af937'

lua54 'yes' -- Enables Lua 5.4 support


author 'X | @103.1'
description 'X Core'

client_script "NativeUI.lua"

dependency 'ox_lib'

shared_scripts {
    '@ox_lib/init.lua'
}



client_scripts {
    'client/*.lua'
}

server_scripts {
    'server/*.lua'
}


ui_page "public/index.html"

files {
	"build/build.js",

	"public/index.html",
	"public/img/*.svg",
	"public/img/*.png",
	"build/img/*",
	"public/*.css",
	"public/*.js",

	"postals.json",
}

postal_file("postals.json")

exports {
	"getPlayerDirection",
	"getPlayerLocation",
	"getCoordsLocation",
	"skillbar",
	"getPlayerZone"
}
