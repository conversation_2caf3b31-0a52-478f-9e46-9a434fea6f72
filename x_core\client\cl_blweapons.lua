local blacklistedWeapons = {
    -- Launchers
    "WEAPON_RPG",
    "WEAPON_GRE<PERSON><PERSON>LAUNCHER",
    "WEAPON_HOMINGLAUNCHER",
    "WEAPON_COMPACTLAUNCHER",
    "WEAPON_FIREWORK",
    "WEAPON_RAILGUN",
    "WEAPON_RAILGUNXM3",

    -- Explosives
    "WEAPON_GRENADE",
    "WEAPON_STICKYBOMB",
    "WEAPON_PROXMINE",
    "WEAPON_PIPEBOMB",
    "WEAPON_MOLOTOV",
    "WEAPON_SMOKEGRENADE",
    "WEAPON_BZGAS",
    "WEAPON_FLARE",

    -- Snipers
    "WEAPON_HEAVYSNIPER",
    "WEAPON_HEAVYSNIPER_MK2",
    "WEAPON_SNIPERRIFLE",
    "WEAPON_MARKSMANRIFLE_MK2",
    "WEAPON_PRECISIONRIFLE",

    -- Heavy weapons
    "WEAPON_<PERSON>NIG<PERSON>",

    -- Melee troll weapons
    "WEAPON_123",

    -- Others (grief/troll/OP weapons)
    "WEAPON_DIGISCANNER",
    "WEAPON_RAYMINIGUN",
    "WEAPON_RAY<PERSON>RBINE",
    "WEAPON_RAYPISTOL",
    "WEAPON_CERAMICPISTOL",
    "WEAPON_NAVYREVOLVER",
    "WEAPON_STUNGUN",
    "WEAPON_STUNGUN_MP",
    "weapon_raycarbine",
    "weapon_emplauncher",
    "weapon_railgunxm3",
    "weapon_grenadelauncher_smoke",
    "weapon_firework",

}

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500) -- Check every 500ms
        local playerPed = PlayerPedId()
        for _, weapon in ipairs(blacklistedWeapons) do
            if HasPedGotWeapon(playerPed, GetHashKey(weapon), false) then
                RemoveWeaponFromPed(playerPed, GetHashKey(weapon))
                TriggerEvent("chatMessage", "X", {190, 0, 0}, "Blacklisted weapon removed!")
            end
        end
    end
end)
