$(function () {
    $("body").hide();
    $("#countdown").hide();
    let ding = document.getElementById('ding')
    let Score = document.getElementById('header2')
    window.addEventListener('message', function(event) {
        var item = event.data;
        if (item.type == 'ui'){
            $("body").fadeIn();
            $("#header").fadeIn();
            $("#aimmenu").fadeIn();
            $("#button").fadeIn();
            $("#score").hide();
        }

        if (item.type == 'ding') {
            ding.play();
        }

        if (item.type == 'score') {
            $("body").show();
            $("#countdown").hide();
            $("#aimmenu").hide();
            $("#button").hide();
            $("#header").hide();
            $("#score").fadeIn();
            
            // Update score
            Score.innerHTML = 'Score : ' + item.score;
        }
    })   
})

function aimtraining(){
    aim = 'aimtraining'
    const selectElement = document.getElementById('weapons');
    const selectedValue = selectElement.value;
    $.post(`https://${GetParentResourceName()}/call`, JSON.stringify({aim:aim, selectedValue:selectedValue}));
    $("#header").hide();
    $("#aimmenu").hide();
    $("#button").hide();
    $("#score").hide();
    $("#countdown").show();
    document.getElementById("countdown").innerHTML = '3';

    setTimeout(function() {
        document.getElementById("countdown").innerHTML = '2';
    }, 1000);
    
    setTimeout(function() {
        document.getElementById("countdown").innerHTML = '1';
    }, 2000);
    
    setTimeout(function() {
        $("body").fadeOut(); 
    }, 2600);
}

function npcroll(){
    aim = 'npcroll'
    const selectElement = document.getElementById('weapons');
    const selectedValue = selectElement.value;
    $.post(`https://${GetParentResourceName()}/call`, JSON.stringify({aim:aim, selectedValue:selectedValue}));
    $("#header").hide();
    $("#aimmenu").hide();
    $("#button").hide();
    $("#score").hide();
    $("#countdown").show();
    document.getElementById("countdown").innerHTML = '3';

    setTimeout(function() {
        document.getElementById("countdown").innerHTML = '2';
    }, 1000);
    
    setTimeout(function() {
        document.getElementById("countdown").innerHTML = '1';
    }, 2000);
    
    setTimeout(function() {
        $("body").fadeOut(); 
    }, 2600);
}

function exit(){
    $.post(`https://${GetParentResourceName()}/exit`, JSON.stringify());
    $("#header").hide();
    $("#aimmenu").hide();
    $("#button").hide();
    $("#score").hide();
    $("body").hide(); 
}

