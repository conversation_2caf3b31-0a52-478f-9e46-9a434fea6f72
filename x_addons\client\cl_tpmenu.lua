-- Configuration
Config = {}

Config.TeleportMenuTitle = "Teleport Menu" -- Menu title
Config.OpenTeleportMenu = "teleport"       -- Command to open menu
Config.TeleportMenuKey = "F6"              -- key to open the teleport menu

-- Teleport Points
Config.TeleportPoints = {
    -- Shown on main screen
    {label = 'Spawn', coords = vector3(-535.3721, -220.8061, 37.6498), category = nil},
    {label = 'Tracking', coords = vector3(-1377.5667, -2945.2593, 13.9450), category = nil},

    -- Arenas
    {label = 'Arena Paintball', coords = vector3(2018.2883, 2785.4575, 53.2301), category = "Arenas"},
    {label = '1v1 Arena', coords = vector3(1142.7307, 194.8378, 258.4543), category = "Arenas"},
    {label = 'Arena 1', coords = vector3(5366.138, -1110.33875, 354.209473), category = "Arenas"},
    {label = 'Arena 2', coords = vector3(4053.767, 0.3427682, 194.9924), category = "Arenas"},
    {label = 'Arena 3', coords = vector3(-147.1069, -4347.702, 190.5014), category = "Arenas"},
    {label = 'Arena 4', coords = vector3(4401.802, 2800.315, 546.6064), category = "Arenas"},
    {label = 'Arena 5', coords = vector3(4014.45166, 1311.133, 677.0786), category = "Arenas"},
    {label = 'Arena 6', coords = vector3(4278.011, 1483.30933, 677.0786), category = "Arenas"},
    {label = 'Arena CS', coords = vector3(-3544.423, 1359.879, 310.34), category = "Arenas"},
    {label = 'Arena Dust', coords = vector3(-3181.3892, -341.5053, 557.8544), category = "Arenas"},
    {label = 'Arena Helizone 1', coords = vector3(1829.8942, -3153.1777, 400.3010), category = "Arenas"},
    {label = 'Arena Helizone 2', coords = vector3(-2552.1018, -1404.3562, 419.3690), category = "Arenas"},
    {label = 'Arena Lego', coords = vector3(-2367.34326, -1153.67566, 328.668121), category = "Arenas"},
    {label = 'Arena Minecraft', coords = vector3(-1954.5708, -1504.37366, 320.060547), category = "Arenas"},
    {label = 'Arena Neon 1', coords = vector3(-2449.079, -1805.707, 100.2199), category = "Arenas"},
    {label = 'Arena Neon 2', coords = vector3(-237.073181, -3393.46753, 558.3144), category = "Arenas"},
    {label = 'Arena Neon 3', coords = vector3(-2181.18, -2366.41553, 500.583221), category = "Arenas"},
    {label = 'Arena Neon 4', coords = vector3(-3202.3916, -475.1443, 318.734955), category = "Arenas"},
    {label = 'Soccer Arena', coords = vector3(-4028.5962, -2232.1536, 55.9345), category = "Arenas"},
    {label = 'Stadium Big', coords = vector3(4342.32, -763.01, 236.65), category = "Arenas"},
    {label = 'Stadium Mid', coords = vector3(4344.56, -1645.90, 236.95), category = "Arenas"},
    {label = 'Stadium Small', coords = vector3(4344.00, -1196.85, 236.58), category = "Arenas"},




    -- Skate Ramps
    {label = 'Sky Ramps', coords = vector3(-2931.83, -3170.93, 1349.42), category = "Skate Ramps"},
    {label = 'Skate Ramps 1', coords = vector3(-2100.26, -1771.64, 650.03), category = "Skate Ramps"},
    {label = 'Skate Ramps 2', coords = vector3(-3353.5784, -815.0250, 102.6333), category = "Skate Ramps"},
    {label = 'Skate Ramps 3', coords = vector3(-2913.331, -1040.35, 100.0), category = "Skate Ramps"},
    {label = 'Skate Park', coords = vector3(-2626.3513, -2271.6323, 1267.6179), category = "Skate Ramps"},

    -- PvP Maps
    {label = 'Nuke Town', coords = vector3(3461.10, -1040.46, 64.2), category = "PvP Maps"},
    {label = 'PvP Map 1', coords = vector3(3444.13, -861.71, 11.42), category = "PvP Maps"},
    {label = 'PvP Map 2', coords = vector3(3606.41, -862.14, 21.79), category = "PvP Maps"},
}

-- Build the teleport menu
local categoryMenus = {}
local mainMenuOptions = {}

for _, tp in ipairs(Config.TeleportPoints) do
    -- Spawn and Tracking go directly on the main screen
    if tp.category == nil then
        table.insert(mainMenuOptions, {
            title = tp.label,
            onSelect = function()
                SetEntityCoords(PlayerPedId(), tp.coords)
            end
        })
    else
        -- All other points go into categories
        if not categoryMenus[tp.category] then
            categoryMenus[tp.category] = {}
            table.insert(mainMenuOptions, {
                title = tp.category,
                menu = 'teleport_' .. tp.category:lower():gsub("%s+", "_")
            })
        end

        table.insert(categoryMenus[tp.category], {
            title = tp.label,
            onSelect = function()
                SetEntityCoords(PlayerPedId(), tp.coords)
            end
        })
    end
end

-- Register each category submenu
for category, options in pairs(categoryMenus) do
    lib.registerContext({
        id = 'teleport_' .. category:lower():gsub("%s+", "_"),
        title = category,
        menu = 'teleport_menu',
        options = options
    })
end

-- Register main teleport menu
lib.registerContext({
    id = 'teleport_menu',
    title = Config.TeleportMenuTitle,
    options = mainMenuOptions
})

-- Command to open the menu
RegisterCommand(Config.OpenTeleportMenu, function()
    lib.showContext('teleport_menu')
end)

-- Key mapping for opening the menu
RegisterKeyMapping(Config.OpenTeleportMenu, 'Teleport Menu', 'keyboard', Config.TeleportMenuKey)
