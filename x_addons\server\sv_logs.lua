local logsEnabled = false
local recentDeaths = {}

-- Authorized Discord IDs
local authorizedDiscordIds = {
    "846599992449302549", --- cruzy
    "discord_id_2",
    "discord_id_2",
    "discord_id_2",
    "discord_id_2",
}

-- Function to get weapon name
local function getWeaponLabel(weaponHash)
    local weapons = {
    [GetHash<PERSON>ey("WEAPON_PISTOL")] = "Pistol",
    [Get<PERSON><PERSON><PERSON><PERSON>("WEAPON_SIN_SCAR")] = "SinCity Scar",
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("WEAPON_SIN_SCARMK2")] = "SinCity Scar MK2",
    [Get<PERSON>ash<PERSON><PERSON>("WEAPON_SIN_FAMAS")] = "SinCity Famas",
    [GetHash<PERSON><PERSON>("WEAPON_SIN_FAMASMK2")] = "SinCity Famas MK2",
    [GetHash<PERSON><PERSON>("WEAPON_SIN_M249")] = "SinCity M249",
    [Get<PERSON><PERSON><PERSON><PERSON>("WEAPON_SIN_PKM")] = "SinCity PKM",
    [GetHash<PERSON><PERSON>("WEAPON_OC_SCAR")] = "OverCast Scar",
    [GetHash<PERSON><PERSON>("WEAPON_OC_AK103")] = "OverCast AK103",
    [GetHashKey("WEAPON_OC_GDEAGLE")] = "OverCast Gold Deagle",
    [GetHashKey("WEAPON_OC_M19")] = "OverCast M19",
    [GetHashKey("WEAPON_OC_P90")] = "OverCast P90",
    [GetHashKey("WEAPON_OC_VHS2")] = "OverCast VHS",
    [GetHashKey("WEAPON_2TAP_DEAGLE")] = "Deagle 2 Tap Headshot",
    [GetHashKey("WEAPON_SIN_AUG")] = "SinCity Aug",
    [GetHashKey("WEAPON_SIN_DEAGLE")] = "SinCity Deagle",
    [GetHashKey("WEAPON_OC_DEAGLE")] = "OverCast Deagle",
    [GetHashKey("WEAPON_WHITE")] = "Pistol White",
    [GetHashKey("WEAPON_PVALO2")] = "pistol_valo2",
    [GetHashKey("WEAPON_PVALO")] = "pistol_valo",
    [GetHashKey("WEAPON_PNERF2")] = "pistol_nerf2",
    [GetHashKey("WEAPON_PNERF")] = "pistol_nerf"
    }
    return weapons[weaponHash] or "unknown"
end

-- Player Join Event (D/C)
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    TriggerClientEvent('x_log:playerJoin', -1, name)
end)

-- Player Leave Event (D/C)
AddEventHandler('playerDropped', function(reason)
    local name = GetPlayerName(source)
    TriggerClientEvent('x_log:playerLeave', -1, name, reason)
end)

-- Command to toggle kill logs (permission check based on Discord ID)
RegisterCommand("logs", function(source, args, rawCommand)
    local identifiers = GetPlayerIdentifiers(source)
    local discordId = nil

    -- Find the player's Discord ID from their identifiers
    for _, id in ipairs(identifiers) do
        if string.sub(id, 1, 8) == "discord:" then
            discordId = string.sub(id, 9)
            break
        end
    end

    -- If no Discord ID is found, deny permission
    if discordId == nil then
        TriggerClientEvent("chat:addMessage", source, {
            args = { "SYSTEM", "You do not have permission to use this command." },
            color = { 190, 0, 0 }
        })
        return
    end

    -- Check if the player is authorized to use the logs command
    local hasPermission = false
    for _, id in ipairs(authorizedDiscordIds) do
        if discordId == id then
            hasPermission = true
            break
        end
    end

    -- Toggle logs if authorized
    if hasPermission then
        logsEnabled = not logsEnabled
        TriggerClientEvent("chat:addMessage", -1, {
            args = { "SYSTEM", "Kill logs " .. (logsEnabled and "enabled" or "disabled") },
            color = { 190, 0, 0 }
        })
    else
        TriggerClientEvent("chat:addMessage", source, {
            args = { "SYSTEM", "You do not have permission to use this command." },
            color = { 190, 0, 0 }
        })
    end
end, false)

-- Death Handler
RegisterServerEvent("killFeed:playerDied")
AddEventHandler("killFeed:playerDied", function(victimId, killerId, deathCause)
    if not logsEnabled then return end

    -- Prevent duplicate logs for the same player
    if recentDeaths[victimId] and (os.time() - recentDeaths[victimId] < 3) then
        return
    end
    recentDeaths[victimId] = os.time()

    -- Validate victim ID
    if not victimId or victimId == -1 then return end

    local victimName = GetPlayerName(victimId) or "Unknown"
    local weapon = getWeaponLabel(deathCause)

    if killerId and killerId ~= -1 and GetPlayerName(killerId) then
        local killerName = GetPlayerName(killerId) or "Unknown"
        TriggerClientEvent("killFeed:sendMessage", -1, 
            ("%s (%d) killed %s (%d) using %s"):format(killerName, killerId, victimName, victimId, weapon))
    else
        TriggerClientEvent("killFeed:sendMessage", -1, 
            ("%s (%d) died to %s"):format(victimName, victimId, weapon))
    end
end)
