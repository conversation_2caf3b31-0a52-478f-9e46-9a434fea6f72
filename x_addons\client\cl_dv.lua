RegisterNetEvent('dv:deleteVehicles')
AddEventHandler('dv:deleteVehicles', function(radius)
    radius = tonumber(radius) or 0
    local playerCoords = GetEntityCoords(PlayerPedId())
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

    if DoesEntityExist(vehicle) then DeleteEntity(vehicle) end

    if radius > 0 then
        for vehicle in EnumerateVehicles() do
            if #(playerCoords - GetEntityCoords(vehicle)) <= radius then DeleteEntity(vehicle) end
        end
    end
end)

function EnumerateVehicles()
    return coroutine.wrap(function()
        local handle, vehicle = FindFirstVehicle()
        if handle ~= -1 then
            repeat coroutine.yield(vehicle) until not FindNextVehicle(handle)
            EndFindVehicle(handle)
        end
    end)
end
