local isDead = false
local cHavePerms = false
local lastHeading = 0
local deadPed = nil
local giveArmour = false
local deathTime = 0

AddEventHandler('onClientMapStart', function()
    exports.spawnmanager:spawnPlayer()
    Citizen.Wait(2500)
    exports.spawnmanager:setAutoSpawn(false)
end)

function revivePed(ped)
    local playerPos = GetEntityCoords(ped, true)
    isDead = false

    -- Resurrect the player locally
    NetworkResurrectLocalPlayer(playerPos, lastHeading, true, true, false)
    SetEntityHeading(ped, lastHeading)
    SetPlayerInvincible(ped, false)
    ClearPedBloodDamage(ped)
    SetEntityHealth(ped, 200)  -- Set full health after revive

    if giveArmour then
        SetPedArmour(PlayerPedId(), 100)  -- Give armour if enabled
    end

    TriggerEvent('playerRevived')

    -- If still dead after revive, clear tasks immediately
    if IsPedDeadOrDying(ped, 1) then
        ClearPedTasksImmediately(ped)
    end

    -- Cleanup any clones of the player that might be stuck around
    cleanupNearbyClones(ped)

    -- Clean up the deadPed entity if it exists
    if deadPed and DoesEntityExist(deadPed) then
        SetEntityAsMissionEntity(deadPed, true, true)
        DeleteEntity(deadPed)
        deadPed = nil
    end

    -- Force the health bar and HUD to update immediately
    Citizen.SetTimeout(100, function()
        -- Ensure proper sync with the NUI
        SendNUIMessage(hud)  -- This forces the HUD to update with the revived player state
    end)
end

function cleanupNearbyClones(playerPed)
    local playerModel = GetEntityModel(playerPed)
    local playerCoords = GetEntityCoords(playerPed)
    local handle, ped = FindFirstPed()
    local success
    repeat
        if DoesEntityExist(ped) and ped ~= playerPed then
            local pedModel = GetEntityModel(ped)
            local pedCoords = GetEntityCoords(ped)
            if pedModel == playerModel and #(playerCoords - pedCoords) < 5.0 then
                SetEntityAsMissionEntity(ped, true, true)
                DeleteEntity(ped)
            end
        end
        success, ped = FindNextPed(handle)
    until not success
    EndFindPed(handle)
end

Citizen.CreateThread(function()
    local playerIndex = NetworkGetPlayerIndex(-1) or 0
    math.randomseed(playerIndex)

    while true do
        Citizen.Wait(0)
        local ped = PlayerPedId()

        if IsEntityDead(ped) then
            if not isDead then
                lastHeading = GetEntityHeading(ped)
                deadPed = ped
                isDead = true
                deathTime = GetGameTimer() + 500
            end

            SetPlayerInvincible(ped, true)

            if GetGameTimer() > deathTime then
                ShowInfoRevive('Press ~b~E ~w~to revive, if bugged use ~p~/resetme')
                if IsControlJustReleased(0, 38) then
                    revivePed(ped)
                end
            end
        end
    end
end)

RegisterCommand("togglearmour", function()
    giveArmour = not giveArmour
    local status = giveArmour and "enabled" or "disabled"
    ShowInfoRevive("Armour on respawn: " .. status)
    if giveArmour then
        Wait(2000)
        ShowInfoRevive("Note: ~r~Health will display incorrectly with this toggled.")
    end
end, false)

TriggerEvent('chat:addSuggestion', '/togglearmour', 'Toggle giving armour on revive')

function ShowInfoRevive(text)
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName(text)
    EndTextCommandThefeedPostTicker(false, true)
end
