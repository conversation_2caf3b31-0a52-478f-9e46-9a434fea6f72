RegisterCommand("setped", function(source, args, rawCommand)
    local targetId = tonumber(args[1])
    local pedModel = args[2]

    if not targetId or not pedModel then
        TriggerClientEvent('chat:addMessage', source, {
            args = { "^1Usage: /setped [id] [ped name]" }
        })
        return
    end

    if GetPlayerPing(targetId) == 0 then
        TriggerClientEvent('chat:addMessage', source, {
            args = { "^1Player ID not found." }
        })
        return
    end

    TriggerClientEvent("x_setPedClient", targetId, pedModel)
end)
