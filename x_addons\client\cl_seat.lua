RegisterCommand("seat", function(source, args)
    local ped = PlayerPedId()
    if not IsPedInAnyVehicle(ped, false) then return end

    local vehicle = GetVehiclePedIsIn(ped, false)
    local seatMap = { [1] = -1, [2] = 0, [3] = 1, [4] = 2, [5] = 3, [6] = 4, [7] = 5, [8] = 6, [9] = 7, [10] = 8 }
    
    local seatIndex = seatMap[tonumber(args[1])]
    if not seatIndex then
        TriggerEvent('chat:addMessage', {color = {255, 0, 0}, args = {"System", "Invalid seat number. Use 1-10."}})
        return
    end
    
    if IsVehicleSeatFree(vehicle, seatIndex) then TaskWarpPedIntoVehicle(ped, vehicle, seatIndex) end
end, false)

TriggerEvent("chat:addSuggestion", "/seat", "1-4", {{name = "Seat", help = "1-10"}})

---- x