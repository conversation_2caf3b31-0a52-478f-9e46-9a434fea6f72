local lobbies = {}
local lobbyCounter = 0
local bucketCounter = 1000
local webhookUrl = 'https://discord.com/api/webhooks/1361231124839399444/uBfT2DePUkQFMaJh9kCnSPlJvKt7fCENxTgC0tjZ_PoQrUpKG-IPQOhk29AEKQkmTkiE'

-- Format time as MM:SS or H:MM:SS
local function formatTime(seconds)
    local hours = math.floor(seconds / 3600)
    local minutes = math.floor((seconds % 3600) / 60)
    local remainingSeconds = seconds % 60
    if hours > 0 then
        return string.format("%d:%02d:%02d", hours, minutes, remainingSeconds)
    else
        return string.format("%02d:%02d", minutes, remainingSeconds)
    end
end

-- Get Discord ID
local function getPlayerDiscordId(playerId)
    local identifiers = GetPlayerIdentifiers(playerId)
    for _, identifier in ipairs(identifiers) do
        if string.match(identifier, "discord:") then
            return string.sub(identifier, 9)
        end
    end
    return "Unknown"
end

-- Send Webhook
local function sendWebhookNotification(lobbyName, password, owner, discordId, timeDeleted, eventType, duration)
    local ownerName = GetPlayerName(owner)
    local ownerDisplay = ownerName and (ownerName .. " (" .. owner .. ")") or ("<@" .. (discordId or "Unknown") .. ">")

    local embed = {
        title = (eventType == 'creation' and "Lobby Created" or "Lobby Deleted"),
        color = (eventType == 'creation' and 65280 or 16711680),
        fields = {
            { name = "**Lobby Name**", value = lobbyName, inline = true },
            { name = "**Password**", value = password or "None", inline = true },
            { name = "**Owner**", value = ownerDisplay, inline = true },
            { name = "**Discord ID**", value = "<@" .. (discordId or "Unknown") .. ">", inline = true }
        },
        footer = { text = "x" },
        timestamp = os.date('!%Y-%m-%dT%H:%M:%SZ')
    }

    if eventType == "creation" then
        table.insert(embed.fields, { name = "**Time Created**", value = os.date('%Y-%m-%d %H:%M:%S'), inline = true })
    elseif eventType == "deletion" then
        table.insert(embed.fields, { name = "**Time Deleted**", value = timeDeleted, inline = true })
        table.insert(embed.fields, { name = "**Time in Lobby**", value = duration or "N/A", inline = true })
    end

    local payload = {
        username = "x",
        embeds = { embed }
    }

    PerformHttpRequest(webhookUrl, function(err, text, headers) end, 'POST', json.encode(payload), { ['Content-Type'] = 'application/json' })
end


-- Create Lobby
RegisterNetEvent('custom:createLobby', function(lobbyName, password)
    local playerId = source
    lobbyCounter += 1
    bucketCounter += 1
    local lobbyId = 'lobby_' .. lobbyCounter

    lobbies[lobbyId] = {
        owner = playerId,
        name = lobbyName,
        password = password,
        bucket = bucketCounter,
        players = { playerId },
        timeElapsed = 0
    }

    SetPlayerRoutingBucket(playerId, bucketCounter)
    local discordId = getPlayerDiscordId(playerId)

    sendWebhookNotification(lobbyName, password, playerId, discordId, nil, 'creation')

    Citizen.CreateThread(function()
        while lobbies[lobbyId] do
            Citizen.Wait(1000)
            if lobbies[lobbyId] then
                lobbies[lobbyId].timeElapsed += 1
                for _, player in ipairs(lobbies[lobbyId].players) do
                    TriggerClientEvent('custom:updateLobbyTimer', player, formatTime(lobbies[lobbyId].timeElapsed))
                end
            end
        end
    end)

    TriggerClientEvent('custom:joinedLobby', playerId, lobbyName, formatTime(lobbies[lobbyId].timeElapsed), 1)

    TriggerClientEvent('ox_lib:notify', playerId, {
        description = 'Lobby "' .. lobbyName .. '" created!',
        type = 'success'
    })
end)

-- Get Lobbies
RegisterNetEvent('custom:getLobbies', function()
    local playerId = source
    local formattedLobbies = {}
    for lobbyId, lobby in pairs(lobbies) do
        formattedLobbies[lobbyId] = {
            name = lobby.name,
            playerCount = #lobby.players,
            maxPlayers = 50,
            time = formatTime(lobby.timeElapsed)
        }
    end
    TriggerClientEvent('custom:receiveLobbies', playerId, formattedLobbies)
end)

-- Join Lobby
RegisterNetEvent('custom:checkLobbyPassword', function(lobbyId, password)
    local playerId = source
    local lobby = lobbies[lobbyId]
    if lobby and lobby.password == password then
        table.insert(lobby.players, playerId)
        SetPlayerRoutingBucket(playerId, lobby.bucket)
        TriggerClientEvent('custom:joinedLobby', playerId, lobby.name, formatTime(lobby.timeElapsed), #lobby.players)
        for _, player in ipairs(lobby.players) do
            TriggerClientEvent('custom:updatePlayerCount', player, #lobby.players)
        end
        TriggerClientEvent('ox_lib:notify', playerId, {
            description = 'Joined lobby: ' .. lobby.name,
            type = 'success'
        })
    else
        TriggerClientEvent('ox_lib:notify', playerId, {
            description = 'Wrong password!',
            type = 'error'
        })
    end
end)

-- Leave Lobby
RegisterNetEvent('custom:leaveLobby', function()
    local playerId = source
    local playerLobby = nil
    for lobbyId, lobby in pairs(lobbies) do
        for i, player in ipairs(lobby.players) do
            if player == playerId then
                playerLobby = lobbyId
                table.remove(lobby.players, i)
                break
            end
        end
    end

    SetPlayerRoutingBucket(playerId, 0)
    TriggerClientEvent('custom:leftLobby', playerId)

    if playerLobby then
        TriggerClientEvent('ox_lib:notify', playerId, {
            description = 'You have left the lobby.',
            type = 'success'
        })

        if #lobbies[playerLobby].players == 0 then
            Citizen.CreateThread(function()
                Citizen.Wait(5000)
                if lobbies[playerLobby] and #lobbies[playerLobby].players == 0 then
                    local bucketId = lobbies[playerLobby].bucket
                    local deletedVehicles = 0
                    local deletionTime = os.date('%Y-%m-%d %H:%M:%S')
                    local discordId = getPlayerDiscordId(lobbies[playerLobby].owner)
                    for _, vehicle in ipairs(GetGamePool("CVehicle")) do
                        if DoesEntityExist(vehicle) and GetEntityRoutingBucket(vehicle) == bucketId then
                            DeleteEntity(vehicle)
                            deletedVehicles += 1
                        end
                    end
                    sendWebhookNotification(
                        lobbies[playerLobby].name,
                        lobbies[playerLobby].password,
                        lobbies[playerLobby].owner,
                        discordId,
                        deletionTime,
                        'deletion',
                        formatTime(lobbies[playerLobby].timeElapsed)
                    )
                    lobbies[playerLobby] = nil
                end
            end)
        end
    end
end)

-- Handle Disconnects
AddEventHandler('playerDropped', function()
    local playerId = source
    for lobbyId, lobby in pairs(lobbies) do
        for i, player in ipairs(lobby.players) do
            if player == playerId then
                table.remove(lobby.players, i)
                break
            end
        end
    end
end)

-- Cleanup Loop
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(900000)
        for lobbyId, lobby in pairs(lobbies) do
            if #lobby.players == 0 then
                lobbies[lobbyId] = nil
            end
        end
    end
end)
