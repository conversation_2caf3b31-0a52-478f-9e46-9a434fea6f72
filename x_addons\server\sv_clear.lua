RegisterCommand("clearall", function(source)
    if source == 0 then -- Console execution
        TriggerClientEvent("clearChat", -1) -- Clears chat for all players
    else
        if IsPlayerAceAllowed(source, "command.clearall") then
            TriggerClientEvent("clearChat", -1) -- Clears chat for all players
        else
            TriggerClientEvent("chat:addMessage", source, {
                color = {190, 0, 0}, -- Red color
                args = {"Error", "You do not have permission to use this command."}
            })
        end
    end
end, false)
