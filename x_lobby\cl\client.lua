local inLobby = false
local lobbyName = ""
local playerCount = 0

-- Open Lobby Menu
local function openLobbyMenu()
    local options = {
        {
            title = 'Create Lobby',
            description = 'Create a new lobby with a password',
            onSelect = function()
                TriggerEvent('custom:createLobby')
            end
        },
        {
            title = 'Active Lobbies',
            description = 'Join an existing lobby',
            onSelect = function()
                TriggerServerEvent('custom:getLobbies')
            end
        },
        {
            title = 'Leave Lobby',
            description = 'Leave your current lobby',
            onSelect = function()
                TriggerServerEvent('custom:leaveLobby')
            end
        }
    }

    lib.registerContext({
        id = 'lobby_menu',
        title = 'Lobby Menu',
        options = options
    })
    lib.showContext('lobby_menu')
end

-- Register the /lobby command
RegisterCommand('lobby', function()
    openLobbyMenu()
end, false)

-- Update Timer UI
RegisterNetEvent('custom:updateLobbyTimer', function(time)
    SendNUIMessage({
        action = "updateLobbyTimer",
        time = time
    })
end)

-- Receive Active Lobbies List
RegisterNetEvent('custom:receiveLobbies', function(lobbies)
    local options = {}

    if next(lobbies) == nil then
        lib.notify({
            title = 'Active Lobbies',
            description = 'No active lobbies available.',
            type = 'error'
        })
        return
    end

    for lobbyId, lobby in pairs(lobbies) do
        local playerCountText = string.format("%d/%d", lobby.playerCount, lobby.maxPlayers)

        table.insert(options, {
            title = lobby.name,
            description = 'Time: ' .. lobby.time .. ' | Players: ' .. playerCountText,
            icon = 'lock',
            onSelect = function()
                local input = lib.inputDialog('Join Lobby', {
                    { type = 'input', label = 'Enter Password', password = true, required = true }
                })

                if input then
                    local password = input[1]
                    TriggerServerEvent('custom:checkLobbyPassword', lobbyId, password)
                end
            end
        })
    end

    lib.registerContext({
        id = 'lobby_list',
        title = 'Available Lobbies',
        options = options
    })
    lib.showContext('lobby_list')
end)

-- Update lobby UI
RegisterNetEvent('custom:updateLobbyStatus', function(status, name, count)
    inLobby = status
    lobbyName = name or ""
    playerCount = count or 0

    if inLobby then
        SendNUIMessage({
            action = "showLobbyStatus",
            lobby = lobbyName,
            players = playerCount
        })
    else
        SendNUIMessage({
            action = "hideLobbyStatus"
        })
    end
end)

-- When player joins a lobby
RegisterNetEvent('custom:joinedLobby', function(name, time, count)
    inLobby = true
    lobbyName = name
    playerCount = count

    SendNUIMessage({
        action = "showLobbyStatus",
        lobby = lobbyName,
        players = playerCount
    })

    TriggerEvent('custom:updateLobbyTimer', time)
end)

-- When player leaves a lobby
RegisterNetEvent('custom:leftLobby', function()
    inLobby = false
    SendNUIMessage({
        action = "hideLobbyStatus"
    })
end)

-- Update player count dynamically
RegisterNetEvent('custom:updatePlayerCount', function(count)
    playerCount = count
    SendNUIMessage({
        action = "updatePlayerCount",
        players = playerCount
    })
end)

-- Create Lobby UI
RegisterNetEvent('custom:createLobby', function()
    local input = lib.inputDialog('Create Lobby', {
        { type = 'input', label = 'Enter Lobby Name', required = true },
        { type = 'input', label = 'Enter Password', password = true, required = true }
    })

    if input then
        local lobbyName = input[1]
        local password = input[2]
        TriggerServerEvent('custom:createLobby', lobbyName, password)

        lib.notify({
            title = 'Lobby Created',
            description = 'Your lobby "' .. lobbyName .. '" has been created successfully!',
            type = 'success'
        })
    end
end)
