-- CONFIG
local fullDVTimer = 60 -- minutes
local softDVTimer = 20 -- minutes

-- Toggles
local fullDVEnabled = true
local softDVEnabled = true

-- Track elapsed time
local elapsedMinutes = 0

-- Send red chat warning
local function sendWarning(msg)
    TriggerClientEvent('chat:addMessage', -1, {
        color = {190, 0, 0},
        args = {'Warning', msg}
    })
end

-- Soft DV: Deletes only unoccupied vehicles
CreateThread(function()
    while true do
        Wait(softDVTimer * 60000)
        elapsedMinutes += softDVTimer

        -- Check for conflict with fullDV
        local minutesUntilFullDV = fullDVTimer - (elapsedMinutes % fullDVTimer)
        if softDVEnabled and (minutesUntilFullDV > 1 and minutesUntilFullDV < fullDVTimer - 1) then
            sendWarning('Unoccupied vehicles will be deleted in 60 Seconds')
            Wait(60000)
            TriggerClientEvent('x_core:softDV', -1)
        else
            print("[AutoDV] Skipping soft DV to avoid overlap with full DV")
        end
    end
end)

-- Full DV: Deletes all vehicles
CreateThread(function()
    while true do
        Wait(fullDVTimer * 60000)
        if fullDVEnabled then
            sendWarning('All vehicles will be deleted in 60 Seconds')
            Wait(60000)

            for _, veh in ipairs(GetAllVehicles()) do
                if DoesEntityExist(veh) then
                    DeleteEntity(veh)
                end
            end

            sendWarning('All vehicles have been deleted')
        end
    end
end)

-- Toggle Soft DV
RegisterCommand('togglesoftdv', function(source)
    softDVEnabled = not softDVEnabled
    local state = softDVEnabled and "enabled" or "disabled"
    TriggerClientEvent('chat:addMessage', source, {
        color = {190, 0, 0},
        args = {'System', 'Soft DV has been ' .. state}
    })
end, true)

-- Toggle Full DV
RegisterCommand('togglefulldv', function(source)
    fullDVEnabled = not fullDVEnabled
    local state = fullDVEnabled and "enabled" or "disabled"
    TriggerClientEvent('chat:addMessage', source, {
        color = {190, 0, 0},
        args = {'System', 'Full DV has been ' .. state}
    })
end, true)
