@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap');

*{
    overflow: hidden;
    margin: 0;
    user-select: none;
    text-shadow: 3px 0px 7px rgba(29, 28, 25, 0.287), -3px 0px 7px rgba(49, 48, 45, 0.22), 0px 4px 7px rgba(51, 50, 46, 0.22);
    font-family: "Bebas Neue", sans-serif;
    font-style: normal;
    color: rgba(245, 245, 245, 1);
}

body{
    background-color: rgba(0, 0, 0, 0.724);
}

#aimmenu{
    display: flex;
    width: 100vw;
    height: 20vw;
    justify-content: center;
}

#countdown{
    font-size: 8.48rem;
    text-align: center;
    margin-top: 22%;
}

#box1 {
    width: 20%;
    height: 48%; 
    margin-top: 6%;
    margin-right: 5%;
    text-align: center;
    background-image: url('npctraining.gif');
    background-size: contain;
    background-repeat: no-repeat;
    border-radius: 8px;
    opacity: 0.8;
    font-size: 1.88rem;

    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    box-shadow: rgba(0, 0, 0, 0.17) 0px -23px 25px 0px inset, rgba(0, 0, 0, 0.15) 0px -36px 30px 0px inset, rgba(0, 0, 0, 0.1) 0px -79px 40px 0px inset, rgba(0, 0, 0, 0.06) 0px 2px 1px, rgba(0, 0, 0, 0.09) 0px 4px 2px, rgba(0, 0, 0, 0.09) 0px 8px 4px, rgba(0, 0, 0, 0.09) 0px 16px 8px, rgba(0, 0, 0, 0.09) 0px 32px 16px;
}

#box1:hover{
    opacity: 1;
    transition: 1s ease;
    box-shadow: rgba(240, 46, 170, 0.4) 0px 5px, rgba(63, 62, 63, 0.3) 0px 10px, rgba(77, 59, 70, 0.2) 0px 15px, rgba(78, 61, 72, 0.1) 0px 20px, rgba(75, 55, 67, 0.05) 0px 25px;
}

#box4{
    width: 20%;
    height: 48%; 
    margin-top: 6%;
    margin-left: 5%;
    text-align: center;
    background-image: url('npcroll.gif');
    background-size: contain;
    background-repeat: no-repeat;
    border-radius: 8px;
    opacity: 0.8;
    font-size: 1.88rem;

    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    box-shadow: rgba(0, 0, 0, 0.17) 0px -23px 25px 0px inset, rgba(0, 0, 0, 0.15) 0px -36px 30px 0px inset, rgba(0, 0, 0, 0.1) 0px -79px 40px 0px inset, rgba(0, 0, 0, 0.06) 0px 2px 1px, rgba(0, 0, 0, 0.09) 0px 4px 2px, rgba(0, 0, 0, 0.09) 0px 8px 4px, rgba(0, 0, 0, 0.09) 0px 16px 8px, rgba(0, 0, 0, 0.09) 0px 32px 16px;
}

#box4:hover{
    opacity: 1;
    transition: 1s ease;
    box-shadow: rgba(240, 46, 170, 0.4) 0px 5px, rgba(63, 62, 63, 0.3) 0px 10px, rgba(77, 59, 70, 0.2) 0px 15px, rgba(78, 61, 72, 0.1) 0px 20px, rgba(75, 55, 67, 0.05) 0px 25px;
}


#box5{
    width: 20%;
    height: 48%; 
    margin-top: 6%;
    margin-left: 20%;
    text-align: center;
    background-image: url('npccar.gif');
    background-size: contain;
    background-repeat: no-repeat;
    border-radius: 8px;
    opacity: 0.8;
    font-size: 1.88rem;

    display: flex; /* Enables flexbox layout */
    flex-direction: column; /* Aligns items in a column (vertical) */
    justify-content: flex-end; /* Aligns the text to the bottom */
    align-items: center; /* Centers the text horizontally */
    box-shadow: rgba(0, 0, 0, 0.17) 0px -23px 25px 0px inset, rgba(0, 0, 0, 0.15) 0px -36px 30px 0px inset, rgba(0, 0, 0, 0.1) 0px -79px 40px 0px inset, rgba(0, 0, 0, 0.06) 0px 2px 1px, rgba(0, 0, 0, 0.09) 0px 4px 2px, rgba(0, 0, 0, 0.09) 0px 8px 4px, rgba(0, 0, 0, 0.09) 0px 16px 8px, rgba(0, 0, 0, 0.09) 0px 32px 16px;
}

#box5:hover{
    opacity: 1;
    transition: 1s ease;
    box-shadow: rgba(240, 46, 170, 0.4) 0px 5px, rgba(63, 62, 63, 0.3) 0px 10px, rgba(77, 59, 70, 0.2) 0px 15px, rgba(78, 61, 72, 0.1) 0px 20px, rgba(75, 55, 67, 0.05) 0px 25px;
}

#header{
    font-size: 4.28rem;
    height: 4%;
    height: 20%;
    width: 100vw;
    margin-top: 2%;
    text-align: center;
    color: rgba(245, 245, 245, 0.856);
}

#header2{
    font-size: 8.28rem;
    height: 100vw;
    width: 100vw;
    margin-top: 20%;
    text-align: center;
    color: rgba(245, 245, 245, 0.856);
}

#score {
    width: 100vw;
    height: 100vw;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#score .exit {
    margin-top: 20px;
    width: 200px;
}

#button {
    width: 100vw;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 0;
    padding: 10px 0;
    box-shadow: rgba(0, 0, 0, 0.17) 0px -23px 25px 0px inset, rgba(0, 0, 0, 0.15) 0px -36px 30px 0px inset, rgba(0, 0, 0, 0.1) 0px -79px 40px 0px inset, rgba(0, 0, 0, 0.06) 0px 2px 1px, rgba(0, 0, 0, 0.09) 0px 4px 2px, rgba(0, 0, 0, 0.09) 0px 8px 4px, rgba(0, 0, 0, 0.09) 0px 16px 8px, rgba(0, 0, 0, 0.09) 0px 32px 16px;
}

#weapons {
    background-color: #25252500;
    color: #ffffffc7;
    font-size: 1.2rem;
    padding: 8px;
    margin-bottom: 10px;
    border: none;
    border-radius: 4px;
    appearance: none;
    text-align: center;
}

.exit {
    text-align: center;
    width: 16%;
    height: 50px;
    background-color: rgba(51, 51, 51, 0.486);
    color: whitesmoke;
    border: none;
    cursor: pointer;
    font-size: 1.68rem;
}

#weapons:focus {
    background-color: rgba(51, 51, 51, 0.486);
    color: #ffffff;
    outline: none;
}

#weapons option {
    background-color: rgba(51, 51, 51, 0.486);
    color: #ffffff;
}

.exit:hover {
    background-color: #ff020260;
    transition: 1s ease;
}

.smalltext{
    font-size: 1rem;
}

#stats {
    width: 100vw;
    text-align: center;
    margin-top: 20px;
}

.stats-container {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
}

.stat {
    background-color: rgba(51, 51, 51, 0.486);
    color: whitesmoke;
    padding: 10px;
    border-radius: 4px;
    width: 30%;
    text-align: center;
}

.stat-label {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}
