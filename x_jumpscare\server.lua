local allowedDiscordIDs = {
    "discord:846599992449302549", --  cruzy
    "discord:1246072771859972158", -- <PERSON><PERSON><PERSON>
    "discord:247542808427233280", -- <PERSON>
    "discord:1112761897368961086", -- <PERSON>
    "discord:674512215486758962", -- skz
    "discord:529618037641445379", -- <PERSON><PERSON>
    "discord:528156804165926915", -- koby

}

local exemptedDiscordIDs = {
    "discord:846599992449302549", -- cruzy
    "discord:1246072771859972158", -- Spagz
    "discord:1105461699777146880", -- <PERSON><PERSON><PERSON>
    "discord:247542808427233280", -- chloe
    "discord:441845504821952544", -- <PERSON>umi
    "discord:842521134409711616", -- <PERSON><PERSON>
    "discord:528156804165926915", -- koby
    "discord:393527909769412608", -- dior



}

local function isAllowed(source)
    if source == 0 then return true end

    for _, identifier in ipairs(GetPlayerIdentifiers(source)) do
        if string.sub(identifier, 1, 8) == "discord:" then
            for _, allowedID in ipairs(allowedDiscordIDs) do
                if identifier == allowedID then
                    return true
                end
            end
        end
    end
    return false
end

local function isExempted(target)
    for _, identifier in ipairs(GetPlayerIdentifiers(target)) do
        if string.sub(identifier, 1, 8) == "discord:" then
            for _, exemptedID in ipairs(exemptedDiscordIDs) do
                if identifier == exemptedID then
                    return true
                end
            end
        end
    end
    return false
end

local jumpscareState = {}
local jumpscareAllState = false

RegisterCommand("jumpscare", function(source, args)
    if not isAllowed(source) then
        if source == 0 then
            print("[Jumpscare] Error: You do not have permission to use this command.")
        else
            TriggerClientEvent("chat:addMessage", source, { color = {190, 0, 0}, args = { "Error", "You do not have permission to use this command." } })
        end
        return
    end

    if not args[1] then
        local errorMessage = "You must provide a player ID."
        if source == 0 then
            print("[Jumpscare] " .. errorMessage)
        else
            TriggerClientEvent("chat:addMessage", source, { color = {190, 0, 0}, args = { "Error", errorMessage } })
        end
        return
    end

    local target = tonumber(args[1])
    if not target or GetPlayerName(target) == nil then
        local errorMessage = "Error: Invalid player ID."
        if source == 0 then
            print("[Jumpscare] " .. errorMessage)
        else
            TriggerClientEvent("chat:addMessage", source, { color = {190, 0, 0}, args = { "Error", errorMessage } })
        end
        return
    end

    if isExempted(target) then
        local errorMessage = "This player is exempt from jumpscares."
        local exemptedMessage = "Player " .. GetPlayerName(source) .. " (" .. source .. ") tried to jumpscare you, but you are exempt."
        
        if source == 0 then
            print("[Jumpscare] " .. errorMessage)
        else
            TriggerClientEvent("chat:addMessage", source, { color = {190, 0, 0}, args = { "Error", errorMessage } })
            TriggerClientEvent("chat:addMessage", target, { color = {190, 0, 190}, args = { "Notice", exemptedMessage } })
        end
        return
    end

    jumpscareState[target] = not jumpscareState[target]
    TriggerClientEvent("jumpscare:toggleNUI", target, jumpscareState[target])

    local stateText = jumpscareState[target] and "enabled" or "disabled"
    local successMessage = "Jumpscare " .. stateText .. " for Player, " .. GetPlayerName(target) .. " (" .. target .. ")"
    
    if source == 0 then
        print("[Jumpscare] " .. successMessage)
    else
        TriggerClientEvent("chat:addMessage", source, { color = {0, 190, 0}, args = { "Success", successMessage } })
    end
end, false)

RegisterCommand("jumpscareall", function(source)
    if not isAllowed(source) then
        if source == 0 then
            print("[Jumpscare] Error: You do not have permission to use this command.")
        else
            TriggerClientEvent("chat:addMessage", source, { color = {190, 0, 0}, args = { "Error", "You do not have permission to use this command." } })
        end
        return
    end

    local players = GetPlayers()
    local affectedPlayers = {}

    jumpscareAllState = not jumpscareAllState

    for _, player in ipairs(players) do
        local playerID = tonumber(player)
        if playerID ~= source and not isExempted(playerID) then
            jumpscareState[playerID] = jumpscareAllState
            TriggerClientEvent("jumpscare:toggleNUI", playerID, jumpscareAllState)
            table.insert(affectedPlayers, GetPlayerName(playerID) .. " (" .. playerID .. ")")
        end
    end

    local stateText = jumpscareAllState and "enabled" or "disabled"

    if #affectedPlayers > 0 then
        local affectedList = table.concat(affectedPlayers, ", ")
        local successMessage = "Jumpscare " .. stateText .. " for, " .. #affectedPlayers .. " players: " .. affectedList

        if source == 0 then
            print("[Jumpscare] " .. successMessage)
        else
            TriggerClientEvent("chat:addMessage", source, { color = {0, 190, 0}, args = { "Success", successMessage } })
        end
    else
        local errorMessage = "No players found to jumpscare."

        if source == 0 then
            print("[Jumpscare] " .. errorMessage)
        else
            TriggerClientEvent("chat:addMessage", source, { color = {190, 0, 0}, args = { "Error", errorMessage } })
        end
    end
end, false)
