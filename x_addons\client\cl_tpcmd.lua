--[[ Config: Define teleportation locations ]]
local teleportLocations = {
    tracking = vector3(-1377.5667, -2945.2593, 12.945), 
    spawn = vector3(-535.3721, -220.8061, 37.6498),

}

--[[ Dynamically create commands for each location ]]
for locationKey, _ in pairs(teleportLocations) do
    RegisterCommand(locationKey, function()
        teleportPlayerTo(locationKey)
    end, false)
end

--[[ Function: Teleport Player to a Location ]]
function teleportPlayerTo(locationKey)
    local location = teleportLocations[locationKey]

    if location then
        local playerPed = PlayerPedId()

        -- Ensure collision is requested at the destination
        RequestCollisionAtCoord(location.x, location.y, location.z)

        -- Teleport the player
        SetEntityCoords(playerPed, location.x, location.y, location.z, false, false, false, true)
        SetEntityHeading(playerPed, 0.0) -- Adjust heading if needed

        -- Notify the player
        TriggerEvent("chat:addMessage", {
         color = {0, 190, 0},
            args = {"x", "Teleported to " .. locationKey .. ""} -- System
        })
    else
        -- Notify the player if the location key is invalid
        TriggerEvent("chat:addMessage", {
         color = {190, 0, 0},
            args = {"x", "Invalid location key: " .. locationKey} -- Error
        })
    end
end
