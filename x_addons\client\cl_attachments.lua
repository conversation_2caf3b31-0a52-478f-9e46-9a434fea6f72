local suppressors = {
    ['WEAPON_SIN_SCAR'] = `COMPONENT_AT_AR_SUPP_02`,
    ['WEAPON_SIN_SCARMK2'] = `COMPONENT_AT_AR_SUPP_02`,
    ['WEAPON_SIN_FAMAS'] = `COMPONENT_AT_AR_SUPP`,
    ['WEAPON_SIN_FAMASMK2'] = `COMPONENT_AT_AR_SUPP`,
    ['WEAPON_SIN_AUG'] = `COMPONENT_SC_AUGA3_SUPP`,
    ['WEAPON_OC_SCAR'] = `COMPONENT_AT_AR_SUPP`,
    ['WEAPON_OC_AK103'] = `COMPONENT_AT_AR_SUPP`,
    ['WEAPON_OC_M19'] = `w_at_m19_supp`,
    ['WEAPON_OC_P90'] = `COMPONENT_P90_SUPP`,
    ['WEAPON_OC_VHS2'] = `COMPONENT_AT_AR_SUPP`,
    ['WEAPON_M9_P_CHROMIUM'] = `w_at_m9_chromium_supp`,
    ['WEAPON_COMBAT_PISTOL_CHROMIUM'] = `w_at_combat_pistol_chromium_supp`,
    ['WEAPON_GLOCK18C'] = `COMPONENT_AT_PI_SUPP`,
    ['WEAPON_M4A1'] = `COMPONENT_AR_M4A1_SUPP`,
    ['WEAPON_2tap_deagle'] = `COMPONENT_AT_AR_SUPP_02`,





}

local scopes = {
    ['WEAPON_SIN_SCARx'] = `COMPONENT_AT_SCOPE_SMALL`,
    ['WEAPON_SIN_SCARMK2'] = `COMPONENT_AT_SCOPE_MEDIUM`,
    ['WEAPON_SIN_FAMAS'] = `COMPONENT_AT_SCOPE_SMALL`,
    ['WEAPON_SIN_FAMASMK2'] = `COMPONENT_FAMASMK2_SCOPE_01`,
    ['WEAPON_SIN_M249'] = `COMPONENT_AT_SCOPE_MEDIUM`,
    ['WEAPON_OC_SCAR'] = `COMPONENT_AT_SCOPE_MEDIUM`,
    ['WEAPON_OC_AK103'] = `COMPONENT_AT_SCOPE_MACRO`,
    ['WEAPON_OC_VHS2'] = `COMPONENT_AT_SCOPE_MEDIUM`,
    ['WEAPON_M9_P_CHROMIUM'] = `w_at_m9_chromium_holo`,
    ['WEAPON_COMBAT_PISTOL_CHROMIUM'] = `w_at_combat_pistol_chromium_holo`,


}

local extendedClips = {
    ['WEAPON_SIN_SCAR'] = `COMPONENT_SCAR_CLIP_02`,
    ['WEAPON_SIN_SCARMK2'] = `COMPONENT_SPECIALCARBINE_CLIP_03`,
    ['WEAPON_SIN_FAMAS'] = `COMPONENT_BULLPUPRIFLE_CLIP_02`,
    ['WEAPON_SIN_FAMASMK2'] = `COMPONENT_FAMASMK2_CLIP_02`,
    ['WEAPON_SIN_M249'] = `COMPONENT_M249_CLIP_02`,
    ['WEAPON_SIN_AUG'] = `COMPONENT_SC_AUGA3_CLIP_02`,
    ['WEAPON_OC_SCAR'] = `COMPONENT_SCARH_CLIP_03`,
    ['WEAPON_OC_AK103'] = `w_ar_ak103_mag2`,
    ['WEAPON_M9_P_CHROMIUM'] = `w_pi_m9_chromium_mag2`,
    ['WEAPON_COMBAT_PISTOL_CHROMIUM'] = `w_pi_combat_pistol_chromium_mag2`,
    ['WEAPON_OC_M19'] = `w_pi_m19_mag2`,
    ['WEAPON_OC_P90'] = `COMPONENT_P90_MAG2`,
    ['WEAPON_OC_VHS2'] = `COMPONENT_VHS2_CLIP_02`,
    ['WEAPON_M4A1'] = `COMPONENT_M4A1_CLIP_02`,
    ['WEAPON_GLOCK18C'] = `COMPONENT_GLOCK18C_CLIP_02`,
    ['WEAPON_2tap_deagle'] = `COMPONENT_PISTOL50_CLIP_02`,




}

local grips = {
    ['WEAPON_SIN_SCAR'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_OC_SCAR'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_SIN_FAMAS'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_SIN_AUG'] = `COMPONENT_AUGA3_GRIP_01`,
    ['WEAPON_MP7HK'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_M4A1'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_SIN_FAMASMK2'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_SIN_SCARMK2'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_OC_AK103'] = `COMPONENT_AT_AR_AFGRIP`,
    ['WEAPON_OC_VHS2'] = `COMPONENT_AT_AR_AFGRIP`,

}

-- Command to handle suppressors
RegisterCommand('supp', function()
    handleWeaponComponent(suppressors, "Suppressor")
end, false)

-- Command to handle scopes
RegisterCommand('scope', function()
    handleWeaponComponent(scopes, "Scope")
end, false)

-- Command to handle extended clips
RegisterCommand('extended', function()
    handleWeaponComponent(extendedClips, "Extended Clip")
end, false)

-- Command to handle grips
RegisterCommand('grip', function()
    handleWeaponComponent(grips, "Grip")
end, false)

-- Common function to handle weapon components
function handleWeaponComponent(componentTable, componentName)
    local playerPed = PlayerPedId()
    local weaponHash = GetSelectedPedWeapon(playerPed)

    if weaponHash and weaponHash ~= `WEAPON_UNARMED` then
        local component = nil

        for weapon, compHash in pairs(componentTable) do
            if weaponHash == GetHashKey(weapon) then
                component = compHash
                break
            end
        end

        if component then
            local hasComponent = HasPedGotWeaponComponent(playerPed, weaponHash, component)

            if hasComponent then
                RemoveWeaponComponentFromPed(playerPed, weaponHash, component)
                TriggerEvent("chat:addMessage", {
                    color = {0, 190, 0},
                    args = {"x",componentName .. " removed."}
                })
            else
                GiveWeaponComponentToPed(playerPed, weaponHash, component)
                TriggerEvent("chat:addMessage", {
                color = {0, 190, 0},
                    args = {"x",componentName .. " added."}
                })
            end
        else
            TriggerEvent("chat:addMessage", {
                color = {190, 0, 0},
                args = {"x", "This weapon does not support a " .. componentName .. "."}
            })
        end
    else
        TriggerEvent("chat:addMessage", {
         color = {190, 0, 0},
            args = {"x", "You are not holding a weapon."}
        })
    end
end
