-- Server-side script for X Aim Labs
-- Player bucket management for instancing
RegisterNetEvent('aim:enterBucket')
AddEventHandler('aim:enterBucket', function()
    local src = source
    local bucketId = src + 10000 -- Create a unique bucket ID for each player
    SetPlayerRoutingBucket(src, bucketId)
    print("Player " .. src .. " entered bucket " .. bucketId)
end)

RegisterNetEvent('aim:exitBucket')
AddEventHandler('aim:exitBucket', function()
    local src = source
    SetPlayerRoutingBucket(src, 0) -- Return player to the main bucket (0)
    print("Player " .. src .. " returned to main bucket")
end)

-- Webhook functionality for logging scores
RegisterNetEvent('aim:logScore')
AddEventHandler('aim:logScore', function(trainingType, score)
    local src = source
    local playerName = GetPlayerName(src)
    
    -- You can add your webhook URL in the config
    if Config.EnableWebhook and Config.WebhookURL ~= "" then
        local embed = {
            {
                ["color"] = ********, -- Red color
                ["title"] = "Aim Training Score",
                ["description"] = "Player: **" .. playerName .. "**\nTraining Type: **" .. trainingType .. "**\nScore: **" .. score .. "**",
                ["footer"] = {
                    ["text"] = "X Aim Labs | " .. os.date("%Y-%m-%d %H:%M:%S")
                }
            }
        }
        
        PerformHttpRequest(Config.WebhookURL, function(err, text, headers) end, 'POST', json.encode({embeds = embed}), { ['Content-Type'] = 'application/json' })
    end
end)




















