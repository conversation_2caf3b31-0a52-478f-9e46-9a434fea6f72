return {
    matchTime = 30000, -- in seconds
    maxRounds = 100,   -- max playable rounds in a duel
    exitDuelCommand = 'exitduel', -- command to exit a duel when you are in one
    weapons = {      -- available weapons to play in duels
        { value = 'WEAPON_SIN_SCAR',     label = 'Scar' },
        { value = 'WEAPON_MK47',     label = 'MK47' },
        { value = 'WEAPON_PISTOL50', label = 'Deagle' },
        { value = 'WEAPON_APPISTOL', label = 'AP Pistol' },
        { value = 'WEAPON_COMBAT_PISTOL_CHROMIUM',  label = 'Chrome Combat Pistol' },
        { value = 'WEAPON_M9_P_CHROMIUM', label = 'M9 Chrome Pistol' },
        { value = 'WEAPON_OC_GDEAGLE', label = 'Golden Deagle' },
        { value = 'WEAPON_DOUBLEACTION', label = 'Golden Rev' },
        { value = 'WEAPON_SIN_DEAGLE', label = 'SinCity Deagle' },
        { value = 'WEAPON_OC_DEAGLE', label = 'OverCast Deagle' },
        { value = 'WEAPON_2TAP_DEAGLE', label = '2 Tap Deagle' },



    },
    maps = {
        {
            value = 'skyramps',
            label = 'Sky Ramps',
            coords = {
                player = {
                    vec4(-2943.06, -3171.18, 1349.17, 0.00),
                    vec4(-2943.06, -3171.18, 1349.17, 0.00),
                    vec4(-2943.06, -3171.18, 1349.17, 0.00),
                    vec4(-2943.06, -3171.18, 1349.17, 0.00),
                    vec4(-2943.06, -3171.18, 1349.17, 0.00),
                },
                opponent = {
                    vec4(-2919.79, -3171.13, 1349.18, 0.00),
                    vec4(-2919.79, -3171.13, 1349.18, 0.00),
                    vec4(-2919.79, -3171.13, 1349.18, 0.00),
                    vec4(-2919.79, -3171.13, 1349.18, 0.00),
                    vec4(-2919.79, -3171.13, 1349.18, 0.00),
                }
            }
        },
        {
            value = 'paint_ball_arena',
            label = 'Paint Ball Arena',
            coords = {
                player = {
                    vec4(2016.19, 2706.72, 49.96, 0.00),
                    vec4(2009.90, 2712.44, 49.69, 0.00),
                    vec4(2005.70, 2718.26, 49.82, 0.0),
                    vec4(2023.89, 2710.83, 50.32, 0.0),
                    vec4(2036.36, 2719.03, 50.42, 0.0),
                },
                opponent = {
                    vec4(2027.81, 2858.16, 50.18, 0.0),
                    vec4(2018.52, 2857.55, 50.23, 0.00),
                    vec4(2003.83, 2853.85, 50.20, 0.00),
                    vec4(2034.13, 2848.77, 50.11, 0.00),
                    vec4(2039.51, 2835.50, 50.31, 0.0),
                }
            }
        },
        {
            value = '1v1arena',
            label = '1v1 Arena',
            coords = {
                player = {
                    vec4(1127.63, 173.38, 258.46, 0.00),
                    vec4(1132.24, 170.69, 258.46, 0.0),
                    vec4(1136.26, 168.32, 258.46, 0.0),
                    vec4(1123.85, 175.90, 258.49, 0.0),
                    vec4(1119.73, 178.12, 258.46, 0.0),
                },
                opponent = {
                    vec4(1153.86, 219.54, 258.49, 0.00),
                    vec4(1150.27, 221.61, 258.49, 0.00),
                    vec4(1146.77, 223.56, 258.49, 0.00),
                    vec4(1157.42, 217.55, 258.49, 0.0),
                    vec4(1162.15, 214.99, 258.49, 0.00),
                }
            }
        },
        {
            value = 'Airport_Termi',
            label = 'Airport terminal',
            coords = {
                player = {
                    vec4(-1034.89, -2781.23, 4.64, 0.00),
                    vec4(-1036.38, -2780.50, 4.64, 0.00),
                    vec4(-1034.53, -2778.48, 4.64, 0.00),
                    vec4(-1032.54, -2778.52, 4.64, 0.00),
                    vec4(-1035.97, -2776.33, 4.64, 0.00),
                },
                opponent = {
                    vec4(-1094.19, -2712.34, 0.81, 0.00),
                    vec4(-1088.96, -2717.29, 0.81, 0.00),
                    vec4(-1082.09, -2711.23, -0.39, 0.00),
                    vec4(-1074.46, -2719.52, 0.81, 0.00),
                    vec4(-1097.38, -2714.87, 0.81, 0.00),
                }
            }
        },
        {
            value = 'cargo_ship',
            label = 'Cargo Ship',
            coords = {
                player = {
                    vec4(1240.66, -2882.95, 9.31, 0.00),
                    vec4(1237.86, -2883.05, 9.32, 0.00),
                    vec4(1233.93, -2882.81, 9.32, 0.00),
                    vec4(1245.45, -2882.55, 9.32, 0.00),
                    vec4(1249.70, -2883.89, 9.32, 0.00),
                },
                opponent = {
                    vec4(1240.01, -3033.50, 9.32, 0.00),
                    vec4(1237.41, -3033.57, 9.34, 0.00),
                    vec4(1232.98, -3033.42, 9.36, 0.00),
                    vec4(1243.87, -3034.16, 9.34, 0.00),
                    vec4(1248.18, -3033.69, 9.36, 0.00),
                }
            }
        },
        {
            value = 'pvp_arena3',
            label = 'PvP Arena 3',
            coords = {
                player = {
                    vec4(-216.41, -4348.75, 191.50, 0.00),
                    vec4(-214.29, -4347.07, 191.50, 0.00),
                    vec4(-214.27, -4350.42, 191.50, 0.00),
                    vec4(-211.78, -4348.85, 191.50, 0.0),
                    vec4(-210.05, -4350.40, 191.50, 0.0),
                },
                opponent = {
                    vec4(-80.23, -4348.08, 191.50, 0.00),
                    vec4(-81.03, -4349.75, 191.50, 0.00),
                    vec4(-81.11, -4346.21, 191.50, 0.00),
                    vec4(-83.12, -4347.88, 191.50, 0.00),
                    vec4(-84.84, -4346.15, 191.50, 0.0),
                }
            }
        },
        {
            value = 'Legion_bank',
            label = 'Legion Bank',
            coords = {
                player = {
                    vec4(145.13, -1063.08, 29.19, 0.00),
                    vec4(147.82, -1064.39, 29.19, 0.00),
                    vec4(149.13, -1063.08, 29.19, 0.00),
                    vec4(150.13, -1063.08, 29.19, 0.00),
                    vec4(147.82, -1064.39, 29.19, 0.00),
                },
                opponent = {
                    vec4(144.72, -1040.43, 29.37, 0.00),
                    vec4(144.72, -1040.43, 29.37, 0.00),
                    vec4(144.72, -1040.43, 29.37, 0.00),
                    vec4(144.72, -1040.43, 29.37, 0.00),
                    vec4(144.72, -1040.43, 29.37, 0.00),
                }
            }
        },
        {
            value = 'nuketown',
            label = 'Nuke Town',
            coords = {
                player = {
                    vec4(3422.09, -1026.20, 64.21, 0.00),
                    vec4(3422.09, -1026.20, 64.21, 0.00),
                    vec4(3422.09, -1026.20, 64.21, 0.00),
                    vec4(3422.09, -1026.20, 64.21, 0.00),
                    vec4(3422.09, -1026.20, 64.21, 0.00),
                },
                opponent = {
                    vec4(3501.49, -1026.70, 64.21, 0.00),
                    vec4(3501.49, -1026.70, 64.21, 0.00),
                    vec4(3501.49, -1026.70, 64.21, 0.00),
                    vec4(3501.49, -1026.70, 64.21, 0.00),
                    vec4(3501.49, -1026.70, 64.21, 0.00),
                }
            }
        },
        {
            value = 'stadiumbig',
            label = 'Stadium Big (5v5)',
            coords = {
                player = {
                    vec4(4353.09, -686.75, 233.78, 0.00),
                    vec4(4353.09, -686.75, 233.78, 0.00),
                    vec4(4353.09, -686.75, 233.78, 0.00),
                    vec4(4353.09, -686.75, 233.78, 0.00),
                    vec4(4353.09, -686.75, 233.78, 0.00),
                },
                opponent = {
                    vec4(4341.11, -837.43, 233.78, 0.00),
                    vec4(4341.11, -837.43, 233.78, 0.00),
                    vec4(4341.11, -837.43, 233.78, 0.00),
                    vec4(4341.11, -837.43, 233.78, 0.00),
                    vec4(4341.11, -837.43, 233.78, 0.00),
                }
            }
        },
        {
            value = 'stadiummid',
            label = 'Stadium Mid',
            coords = {
                player = {
                    vec4(4343.85, -1666.55, 233.78, 0.00),
                    vec4(4343.85, -1666.55, 233.78, 0.00),
                    vec4(4343.85, -1666.55, 233.78, 0.00),
                    vec4(4343.85, -1666.55, 233.78, 0.00),
                    vec4(4343.85, -1666.55, 233.78, 0.00),
                },
                opponent = {
                    vec4(4344.25, -1601.02, 233.78, 0.00),
                    vec4(4344.25, -1601.02, 233.78, 0.00),
                    vec4(4344.25, -1601.02, 233.78, 0.00),
                    vec4(4344.25, -1601.02, 233.78, 0.00),
                    vec4(4344.25, -1601.02, 233.78, 0.00),
                }
            }
        },
        {
            value = 'stadiumsmall',
            label = 'Stadium Small',
            coords = {
                player = {
                    vec4(4343.83, -1232.38, 233.78, 0.00),
                    vec4(4343.83, -1232.38, 233.78, 0.00),
                    vec4(4343.83, -1232.38, 233.78, 0.00),
                    vec4(4343.83, -1232.38, 233.78, 0.00),
                    vec4(4343.83, -1232.38, 233.78, 0.00),
                },
                opponent = {
                    vec4(4343.72, -1161.39, 233.78, 0.00),
                    vec4(4343.72, -1161.39, 233.78, 0.00),
                    vec4(4343.72, -1161.39, 233.78, 0.00),
                    vec4(4343.72, -1161.39, 233.78, 0.00),
                    vec4(4343.72, -1161.39, 233.78, 0.00),
                }
            }
        }
    }
}