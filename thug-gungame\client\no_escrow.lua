RegisterNetEvent('thug-gungame:join-after', function()
    -- # If you have an event that you want to be triggered when a player join gungame, you can write it here.
end)

RegisterNetEvent('thug-gungame:leave-after', function()
    -- If you have an event that you want to be triggered when a player leave gungame, you can write it here.
end)

RegisterNetEvent('thug-gungame:winnerPrize', function()
    -- # In this section you can specify an event for the prize that the Gungame round winner will receive.
end)

RegisterNetEvent('thug-gungame:finishGame', function()
    -- # In this section, you can specify if there is an event that you want to happen for all players when a round ends.
end)

RegisterNetEvent('thug-gungame:reviveEvent', function()
    local playerPed = PlayerPedId()

    if IsEntityDead(playerPed) then
        DoScreenFadeOut(1000)
        Wait(1000)

        -- Revive player at the same location
        local coords = GetEntityCoords(playerPed)
        NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, GetEntityHeading(playerPed), true, false)

        -- Cleanup and reset player state
        ClearPedBloodDamage(playerPed)
        ResetPedMovementClipset(playerPed, 0.0)
        SetEntityHealth(playerPed, 200)
        DoScreenFadeIn(1000)
    end
end)
