CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        local vehicle = GetVehiclePedIsIn(playerPed, false)

        -- Check if the player is in a helicopter or plane (class 15 or 16)
        if vehicle and (GetVehicleClass(vehicle) == 15 or GetVehicleClass(vehicle) == 16) then
            local mainRotorHealth = GetHeliMainRotorHealth(vehicle)
            local tailRotorHealth = GetHeliTailRotorHealth(vehicle)
            local engineHealth = GetVehicleEngineHealth(vehicle)
            local altitude = math.floor(GetEntityHeightAboveGround(vehicle) * 3.28084) -- Convert meters to feet
            local roll = math.floor(GetEntityRoll(vehicle))
            local pitch = math.floor(GetEntityPitch(vehicle))

            -- Function to determine color based on health
            local function getHealthColor(health)
                if health > 750 then return "~q~" elseif health > 400 then return "~o~" else return "~r~" end
            end

            -- Set colors based on health
            local mainRotorColor = getHealthColor(mainRotorHealth)
            local tailRotorColor = getHealthColor(tailRotorHealth)
            local engineColor = getHealthColor(engineHealth)

            -- Draw text with the requested layout, ensuring health indicators are colored and Roll/Altitude/Pitch are white
            SetTextFont(4)
            SetTextScale(0.45, 0.45) -- Adjust text size
            SetTextColour(255, 255, 255, 255) -- Default to white
            SetTextOutline()

            SetTextEntry("STRING")
            AddTextComponentString(string.format(
                "%sMain Rotor | %sTail Rotor | %sENG\n~w~Roll: %d  Altitude: %d ft  Pitch: %d",
                mainRotorColor, tailRotorColor, engineColor, roll, altitude, pitch
            ))
            DrawText(0.02, 0.75) -- Moved the text up a bit more

            Wait(0) -- Keep UI continuously on screen
        else
            Wait(500) -- Reduce CPU usage when not in an aircraft
        end
    end
end)
