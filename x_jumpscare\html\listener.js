$(function() {
    window.addEventListener("message", (event) => {
        var item = event.data;
        if (item !== undefined && item.type === "ui") {
            if (item.display) {
                $('#container').show();
                let audio = $('#aids')[0];
                audio.currentTime = 0;  // Reset the audio to the start
                let playPromise = audio.play();
                
                if (playPromise !== undefined) {
                    playPromise.catch(error => console.warn("Audio play prevented:", error));
                }
            } else {
                $('#container').hide();
                let audio = $('#aids')[0];
                audio.pause();
                audio.currentTime = 0;  // Reset audio when hidden
            }
        }
    });
});
