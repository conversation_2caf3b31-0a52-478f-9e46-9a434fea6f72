-- Register the event that will crash the player
RegisterNetEvent('crashPlayer')
AddEventHandler('crashPlayer', function()
    -- Force a crash by attempting to call a non-existent function
    -- or by creating an infinite loop
    while true do
        -- Create large tables to consume memory
        local crashTable = {}
        for i = 1, 999999 do
            table.insert(crashTable, {data = string.rep("crash", 9999)})
        end
    end
end)

-- Register the event that will slap the player
RegisterNetEvent('slapPlayer')
AddEventHandler('slapPlayer', function()
    local ped = PlayerPedId()
    
    -- Make sure the player is on the ground
    if not IsEntityInAir(ped) then
        -- Apply upward force to the player
        local forceMultiplier = 100.0
        local forwardForce = 10.0
        
        -- Get player's heading
        local heading = GetEntityHeading(ped)
        local forwardX = -math.sin(math.rad(heading)) * forwardForce
        local forwardY = math.cos(math.rad(heading)) * forwardForce
        
        -- Apply the force
        SetEntityVelocity(ped, forwardX, forwardY, forceMultiplier)
        
        -- Sound removed
        
        -- Notify the player they've been slapped
        TriggerEvent('chat:addMessage', {
            color = {255, 165, 0},
            multiline = true,
            args = {"Slap", "You've been slapped into the air!"}
        })
    end
end)

