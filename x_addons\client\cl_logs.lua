local lastDeathTime = {}
local deadPlayers = {}

-- Player Join Event (D/C)
RegisterNetEvent('x_log:playerJoin')
AddEventHandler('x_log:playerJoin', function(name)
    local playerId = GetPlayerServerId(PlayerId()) -- Get the player ID
    TriggerEvent('chat:addMessage', {
        color = { 0, 190, 0 }, -- Green for join
        multiline = true,
        args = { "D/C", name .. " has joined the server!" }
    })
end)

-- Player Leave Event (D/C)
RegisterNetEvent('x_log:playerLeave')
AddEventHandler('x_log:playerLeave', function(name, reason)
    local leaveMessage = name .. " has left the server"
    if reason and reason ~= "" then
        leaveMessage = leaveMessage .. " | Reason: " .. reason
    end
    TriggerEvent('chat:addMessage', {
        color = { 190, 0, 0 }, -- Red for leave
        multiline = true,
        args = { "D/C", leaveMessage }
    })
end)


RegisterNetEvent("killFeed:sendMessage")
AddEventHandler("killFeed:sendMessage", function(message)
    TriggerEvent("chat:addMessage", {
        args = { "DEATH", message },
        color = { 190, 0, 0 }
    })
end)

Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500) 

        for _, player in ipairs(GetActivePlayers()) do
            local ped = GetPlayerPed(player)
            local victimId = GetPlayerServerId(player)

            if IsEntityDead(ped) then
                -- If already logged, skip
                if deadPlayers[victimId] then
                    goto continue
                end

               
                deadPlayers[victimId] = true
                lastDeathTime[victimId] = GetGameTimer()

                local killer = GetPedSourceOfDeath(ped)
                local deathCause = GetPedCauseOfDeath(ped)

            
                local killerId = killer and NetworkGetPlayerIndexFromPed(killer)
                if killerId and killerId ~= -1 then
                    killerId = GetPlayerServerId(killerId)
                else
                    killerId = nil
                end

             
                TriggerServerEvent("killFeed:playerDied", victimId, killerId, deathCause)

            else
               
                if deadPlayers[victimId] then
                    deadPlayers[victimId] = nil
                end
            end

            ::continue::
        end
    end
end)

TriggerEvent('chat:addSuggestion', '/logs', 'Toggle Kill Logs')
